#define DR_WAV_IMPLEMENTATION
#include "dr_wav.h"
#define DR_MP3_IMPLEMENTATION
#include "dr_mp3.h"

#include "AudioSample.h"
#include "AudioEngine.h"
#include <android/log.h>
#include <cmath>
#include <algorithm>
#include <cerrno>
#include <cstring>
#include <inttypes.h>


#define APP_TAG "AudioSample"
#define ALOGI(...) __android_log_print(ANDROID_LOG_INFO, APP_TAG, __VA_ARGS__)
#define ALOGE(...) __android_log_print(ANDROID_LOG_ERROR, APP_TAG, __VA_ARGS__)
#define ALOGW(...) __android_log_print(ANDROID_LOG_WARN, APP_TAG, __VA_ARGS__)

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

constexpr int NUM_TAPS = 15; // Optimal balance for performance and quality
constexpr int SUBDIVISION_STEPS = 82; // Your recommended sweet spot
constexpr double KAISER_BETA = 6.5; // Slightly higher for buttery smooth interpolation

// Static member definitions
std::vector<std::vector<float>> AudioSample::sincTable;
bool AudioSample::sincTableInitialized = false;
const uint32_t AudioSample::PERSISTENT_CACHE_MAGIC_NUMBER;
const uint32_t AudioSample::PERSISTENT_CACHE_VERSION;


bool AudioSample::saveToPersistentCache(const std::string& cacheFilePath, uint64_t modificationTimestamp) {
    if (audioData.empty() || totalFrames == 0 || channels == 0 || sampleRate == 0) {
        ALOGE("AudioSample::saveToPersistentCache: No valid audio data to save for %s.", filePath.c_str());
        return false;
    }
    if (cacheFilePath.empty()) {
        ALOGE("AudioSample::saveToPersistentCache: Cache file path is empty for %s.", filePath.c_str());
        return false;
    }

    FILE* outFile = fopen(cacheFilePath.c_str(), "wb");
    if (!outFile) {
        ALOGE("AudioSample::saveToPersistentCache: Failed to open cache file %s for writing. Error: %s", cacheFilePath.c_str(), strerror(errno));
        return false;
    }

    bool success = true;
    size_t written = 0;

    written = fwrite(&PERSISTENT_CACHE_MAGIC_NUMBER, sizeof(PERSISTENT_CACHE_MAGIC_NUMBER), 1, outFile);
    success &= (written == 1);
    written = fwrite(&PERSISTENT_CACHE_VERSION, sizeof(PERSISTENT_CACHE_VERSION), 1, outFile);
    success &= (written == 1);
    written = fwrite(&modificationTimestamp, sizeof(modificationTimestamp), 1, outFile);
    success &= (written == 1);
    written = fwrite(&sampleRate, sizeof(sampleRate), 1, outFile);
    success &= (written == 1);
    written = fwrite(&channels, sizeof(channels), 1, outFile);
    success &= (written == 1);
    written = fwrite(&totalFrames, sizeof(totalFrames), 1, outFile);
    success &= (written == 1);

    if (!success) {
        ALOGE("AudioSample::saveToPersistentCache: Failed to write header to %s.", cacheFilePath.c_str());
        fclose(outFile);
        remove(cacheFilePath.c_str());
        return false;
    }

    written = fwrite(audioData.data(), sizeof(float), audioData.size(), outFile);
    if (written != audioData.size()) {
        ALOGE("AudioSample::saveToPersistentCache: Failed to write complete audio data to %s (wrote %zu of %zu floats).", cacheFilePath.c_str(), written, audioData.size());
        success = false;
        fclose(outFile);
        remove(cacheFilePath.c_str());
        return false;
    }

    fclose(outFile);
    if (success) {
        ALOGI("AudioSample::saveToPersistentCache: Successfully saved processed audio for %s to %s (ModTime: %" PRIu64 ")", filePath.c_str(), cacheFilePath.c_str(), modificationTimestamp);
    }
    return success;
}

bool AudioSample::loadFromPersistentCache(const std::string& cacheFilePath, uint64_t expectedModificationTimestamp, const std::string& originalFilePathHint) {
    if (cacheFilePath.empty()) {
        ALOGW("AudioSample::loadFromPersistentCache: Cache file path is empty for hint %s.", originalFilePathHint.c_str());
        return false;
    }

    FILE* inFile = fopen(cacheFilePath.c_str(), "rb");
    if (!inFile) {
        ALOGI("AudioSample::loadFromPersistentCache: Cache file %s not found or cannot be opened (this is normal if not cached yet).", cacheFilePath.c_str());
        return false;
    }

    uint32_t magic = 0, version = 0;
    uint64_t modTime = 0;
    uint32_t sr = 0;
    int32_t ch = 0, frames = 0;
    bool success = true;
    size_t readItems = 0;

    readItems = fread(&magic, sizeof(magic), 1, inFile);
    success &= (readItems == 1 && magic == PERSISTENT_CACHE_MAGIC_NUMBER);
    if (!success) { ALOGW("AudioSample::loadFromPersistentCache: Magic number mismatch or read error for %s.", cacheFilePath.c_str()); fclose(inFile); return false; }

    readItems = fread(&version, sizeof(version), 1, inFile);
    success &= (readItems == 1 && version == PERSISTENT_CACHE_VERSION);
    if (!success) { ALOGW("AudioSample::loadFromPersistentCache: Version mismatch or read error for %s.", cacheFilePath.c_str()); fclose(inFile); return false; }

    readItems = fread(&modTime, sizeof(modTime), 1, inFile);
    success &= (readItems == 1);
    if (!success) { ALOGW("AudioSample::loadFromPersistentCache: ModTime read error for %s.", cacheFilePath.c_str()); fclose(inFile); return false; }

    if (modTime != expectedModificationTimestamp) {
        ALOGI("AudioSample::loadFromPersistentCache: Stale cache for %s. Expected ModTime: %" PRIu64 ", Cached ModTime: %" PRIu64 ".", cacheFilePath.c_str(), expectedModificationTimestamp, modTime);
        fclose(inFile);
        return false;
    }

    readItems = fread(&sr, sizeof(sr), 1, inFile);
    success &= (readItems == 1);
    readItems = fread(&ch, sizeof(ch), 1, inFile);
    success &= (readItems == 1);
    readItems = fread(&frames, sizeof(frames), 1, inFile);
    success &= (readItems == 1);

    if (!success || sr == 0 || ch == 0 || frames == 0) {
        ALOGE("AudioSample::loadFromPersistentCache: Invalid header data (SR/CH/Frames zero or read error) in %s.", cacheFilePath.c_str());
        fclose(inFile);
        return false;
    }

    this->audioData.resize(static_cast<size_t>(frames) * ch);
    readItems = fread(this->audioData.data(), sizeof(float), this->audioData.size(), inFile);
    if (readItems != this->audioData.size()) {
        ALOGE("AudioSample::loadFromPersistentCache: Failed to read complete audio data from %s (read %zu of %zu floats).", cacheFilePath.c_str(), readItems, this->audioData.size());
        this->audioData.clear();
        fclose(inFile);
        return false;
    }

    fclose(inFile);

    this->filePath = originalFilePathHint;
    this->sampleRate = sr;
    this->channels = ch;
    this->totalFrames = frames;
    this->isPlaying.store(false);
    this->preciseCurrentFrame.store(0.0f);
    this->currentTick.store(0);
    this->doublePrecisionFrame = 0.0;
    this->playOnceThenLoopSilently = false;
    this->playedOnce = false;

    ALOGI("AudioSample::loadFromPersistentCache: Successfully loaded processed audio for %s from %s (ModTime: %" PRIu64 ")", this->filePath.c_str(), cacheFilePath.c_str(), modTime);
    return true;
}

double AudioSample::bessel_i0_approx(double x) {
    double ax = std::abs(x);
    if (ax < 3.75) {
        double y = x / 3.75;
        y *= y;
        return 1.0 + y * (3.5156229 + y * (3.0899424 + y * (1.2067492 + y * (0.2659732 + y * (0.0360768 + y * 0.0045813)))));
    } else {
        double y = 3.75 / ax;
        return (std::exp(ax) / std::sqrt(ax)) * (0.39894228 + y * (0.01328592 + y * (0.00225319 + y * (-0.00157565 + y * (0.00916281 + y * (-0.02057706 + y * (0.02635537 + y * (-0.01647633 + y * 0.00392377))))))));
    }
}

double AudioSample::kaiserWindow(double n_rel_to_center, double N_total_taps, double beta) {
    if (std::abs(n_rel_to_center) > (N_total_taps / 2.0 - 0.5) && N_total_taps > 1) {
        return 0.0;
    }
    double term_val_for_bessel_arg;
    if (N_total_taps <= 1) term_val_for_bessel_arg = 0.0;
    else term_val_for_bessel_arg = (2.0 * (n_rel_to_center + (N_total_taps/2.0 - 0.5)) / (N_total_taps - 1.0)) - 1.0;

    double val_inside_sqrt = 1.0 - term_val_for_bessel_arg * term_val_for_bessel_arg;
    if (val_inside_sqrt < 0) val_inside_sqrt = 0;

    return bessel_i0_approx(beta * std::sqrt(val_inside_sqrt)) / bessel_i0_approx(beta);
}

void AudioSample::precalculateSincTable() {
    if (sincTableInitialized) return;

    sincTable.resize(SUBDIVISION_STEPS, std::vector<float>(NUM_TAPS));
    for (int j = 0; j < SUBDIVISION_STEPS; ++j) {
        double fractionalOffset = static_cast<double>(j) / SUBDIVISION_STEPS;
        float sumCoeffs = 0.0f;

        for (int i = 0; i < NUM_TAPS; ++i) {
            double sincPoint = (static_cast<double>(i) - (NUM_TAPS / 2.0 - 1.0)) - fractionalOffset;
            double sincValue;
            if (std::abs(sincPoint) < 1e-9) {
                sincValue = 1.0;
            } else {
                sincValue = std::sin(M_PI * sincPoint) / (M_PI * sincPoint);
            }
            double kaiser_n_rel = static_cast<double>(i) - (NUM_TAPS - 1.0) / 2.0;
            double windowValue = kaiserWindow(kaiser_n_rel, NUM_TAPS, KAISER_BETA);

            sincTable[j][i] = static_cast<float>(sincValue * windowValue);
            sumCoeffs += sincTable[j][i];
        }

        if (std::abs(sumCoeffs) > 1e-6) {
            for (int i = 0; i < NUM_TAPS; ++i) {
                sincTable[j][i] /= sumCoeffs;
            }
        }
    }
    sincTableInitialized = true;
    ALOGI("Sinc table precalculated: %d steps, %d taps. Beta: %f", SUBDIVISION_STEPS, NUM_TAPS, KAISER_BETA);
}

bool AudioSample::hasExtension(const std::string& path, const std::string& extension) {
    if (path.length() >= extension.length()) {
        std::string lowerFilePath = path;
        std::transform(lowerFilePath.begin(), lowerFilePath.end(), lowerFilePath.begin(),
                       [](unsigned char c){ return std::tolower(c); });
        return (0 == lowerFilePath.compare(lowerFilePath.length() - extension.length(), extension.length(), extension));
    }
    return false;
}

bool AudioSample::tryLoadPath(AAssetManager* assetManager, const std::string& currentPathToTry) {
    audioData.clear(); totalFrames = 0; channels = 0; sampleRate = 0; bool success = false;
    AAsset* asset = AAssetManager_open(assetManager, currentPathToTry.c_str(), AASSET_MODE_BUFFER);
    if (!asset) { ALOGW("AudioSample: Failed to open asset: %s", currentPathToTry.c_str()); return false; }
    const void* assetBuffer = AAsset_getBuffer(asset);
    size_t assetLength = AAsset_getLength(asset);
    if (!assetBuffer) { ALOGW("AudioSample: Failed to get asset buffer for: %s", currentPathToTry.c_str()); AAsset_close(asset); return false; }

    if (hasExtension(currentPathToTry, ".wav")) {
        drwav wav;
        if (drwav_init_memory(&wav, assetBuffer, assetLength, nullptr)) {
            channels = wav.channels; totalFrames = (int32_t)wav.totalPCMFrameCount; sampleRate = wav.sampleRate;
            audioData.resize(static_cast<size_t>(totalFrames) * channels);
            success = (drwav_read_pcm_frames_f32(&wav, totalFrames, audioData.data()) == static_cast<drwav_uint64>(totalFrames));
            drwav_uninit(&wav);
        }
    } else if (hasExtension(currentPathToTry, ".mp3")) {
        drmp3_config config; drmp3_uint64 pcmFrameCount;
        float* pPcmFrames = drmp3_open_memory_and_read_pcm_frames_f32(assetBuffer, assetLength, &config, &pcmFrameCount, nullptr);
        if (pPcmFrames) {
            channels = config.channels; sampleRate = config.sampleRate; totalFrames = (int32_t)pcmFrameCount;
            audioData.assign(pPcmFrames, pPcmFrames + (static_cast<size_t>(pcmFrameCount) * channels));
            drmp3_free(pPcmFrames, nullptr); success = true;
        }
    }
    AAsset_close(asset); return success;
}

void AudioSample::load(AAssetManager* assetManager, const std::string& basePath, AudioEngine* engine, uint64_t modificationTimestamp) {
    if (!sincTableInitialized) { precalculateSincTable(); }
    this->audioEnginePtr = engine;
    this->filePath = basePath;

    ALOGI("AudioSample::load (Asset): Attempting to load: %s, ModTime: %" PRIu64, basePath.c_str(), modificationTimestamp);
    isPlaying.store(false); preciseCurrentFrame.store(0.0f); currentTick.store(0); doublePrecisionFrame = 0.0; useEngineRateForPlayback_.store(false);
    playedOnce = false; loop.store(false); playOnceThenLoopSilently = false;

    if (!assetManager) { ALOGE("AudioSample::load (Asset): AssetManager is null for %s!", basePath.c_str()); return; }
    if (!this->audioEnginePtr) { ALOGE("AudioSample::load (Asset): AudioEngine pointer is null for %s!", basePath.c_str()); return; }

    std::string persistentCacheFilePath = this->audioEnginePtr->getPersistentCacheFilePath(basePath, modificationTimestamp);
    if (!persistentCacheFilePath.empty()) {
        if (loadFromPersistentCache(persistentCacheFilePath, modificationTimestamp, basePath)) {
            ALOGI("AudioSample::load (Asset): Successfully loaded '%s' from persistent cache: %s", basePath.c_str(), persistentCacheFilePath.c_str());
            // Ensure stream sample rate and standardized vinyl length even for cached loads
            if (this->audioEnginePtr->getStreamSampleRate() > 0 && this->sampleRate != this->audioEnginePtr->getStreamSampleRate()) {
                ALOGW("AudioSample::load (Asset): Cached sample rate %u for '%s' differs from stream rate %u. Resampling.", this->sampleRate, basePath.c_str(), this->audioEnginePtr->getStreamSampleRate());
                resampleDataTo(this->audioEnginePtr->getStreamSampleRate());
            }
            // CRITICAL: Standardize to 25 RPM base length so angle mapping is consistent across files
            standardizeToVinylLength();
            return;
        } else {
            ALOGI("AudioSample::load (Asset): Persistent cache miss or stale for '%s' (Path: %s). Loading from assets.", basePath.c_str(), persistentCacheFilePath.c_str());
        }
    } else {
         ALOGW("AudioSample::load (Asset): Could not generate persistent cache file path for %s. Proceeding without persistent cache.", basePath.c_str());
    }


    bool loadedSuccessfully = false; std::string successfulPathAttempt;
    if (hasExtension(basePath, ".wav") || hasExtension(basePath, ".mp3")) {
        if (tryLoadPath(assetManager, basePath)) { loadedSuccessfully = true; successfulPathAttempt = basePath; }
    }
    if (!loadedSuccessfully) {
        std::string pathWithMp3 = basePath + ".mp3";
        if (tryLoadPath(assetManager, pathWithMp3)) { loadedSuccessfully = true; successfulPathAttempt = pathWithMp3; }
    }
    if (!loadedSuccessfully) {
        std::string pathWithWav = basePath + ".wav";
        if (tryLoadPath(assetManager, pathWithWav)) { loadedSuccessfully = true; successfulPathAttempt = pathWithWav; }
    }

    if (loadedSuccessfully) {
        this->filePath = successfulPathAttempt;
        ALOGI("AudioSample::load (Asset): Successfully decoded asset '%s' (Frames: %d, Ch: %d, SR: %u Hz)", this->filePath.c_str(), totalFrames, channels, sampleRate);

        if (this->audioEnginePtr->getStreamSampleRate() > 0) {
            uint32_t targetSr = this->audioEnginePtr->getStreamSampleRate();
            if (this->sampleRate != targetSr && targetSr > 0 && this->sampleRate > 0) {
                ALOGI("AudioSample::load (Asset): Resampling asset '%s' from %u Hz to %u Hz", this->filePath.c_str(), this->sampleRate, targetSr);
                resampleDataTo(targetSr);
                ALOGI("AudioSample::load (Asset): Resampling complete for asset '%s'. New frames: %d, new SR: %u", this->filePath.c_str(), this->totalFrames, this->sampleRate);
            } else {
                ALOGI("AudioSample::load (Asset): No resampling needed for asset '%s' (Original SR: %u Hz, Target SR: %u Hz)", this->filePath.c_str(), this->sampleRate, targetSr);
            }
            
            // CRITICAL FIX: Automatically standardize to vinyl length for perfect sync
            standardizeToVinylLength();
            
            if (!persistentCacheFilePath.empty()) {
                saveToPersistentCache(persistentCacheFilePath, modificationTimestamp);
            }
        }
    } else {
        ALOGE("AudioSample::load (Asset): Failed to load ASSET for base '%s'", basePath.c_str());
        audioData.clear(); totalFrames = 0; channels = 0; sampleRate = 0;
    }
}

void AudioSample::getAudio(float* outputBuffer, int32_t numOutputFrames, int32_t outputStreamChannels,
                           float effectiveVolume) {
    // Note: Removed verbose GETAUDIO_CALL_TRACKING_CPP logging for performance

    // Load position tracking systems  
    float localPreciseCurrentFrame = preciseCurrentFrame.load();
    int64_t localCurrentTick = currentTick.load();
    float playbackRateToUse = 1.0f;

    // ===== CRITICAL FIX: DISABLE BROKEN CORRUPTION DETECTION =====
    // The static variable approach was causing false positives due to variable scope conflicts
    // Real position corruption would be detected by the atomic preciseCurrentFrame consistency checks
    // Focus on actual audio continuity rather than tracking static variables across function calls
    
    // Note: Removed verbose LOAD_DEBUG logging for performance

    // Get master tick system for tick-based tracking
    MasterTickSystem* masterTickSystem = nullptr;
    if (audioEnginePtr) {
        masterTickSystem = audioEnginePtr->getMasterTickSystem();
    }

    // Snapshot engine rate mode once per buffer to avoid mode flips mid-buffer
    bool engineRateMode = useEngineRateForPlayback_.load();

    // Get playback rate from audio engine once at buffer start (snapshot semantics)
    // This avoids mid-buffer rate re-evaluation that can cause audible stutters when UI updates overlap
    if (engineRateMode && audioEnginePtr != nullptr) {
        playbackRateToUse = audioEnginePtr->platterTargetPlaybackRate_.load();
        // Note: We intentionally do NOT re-check the rate mid-buffer. Changes take effect next buffer.
    }

    if (!isPlaying.load() || audioData.empty() || totalFrames == 0 || channels == 0) {
        return;
    }

    // TEMPORARILY DISABLED: Convert tick-based position to frame position if using tick system
    // This was causing audio playback issues, so reverting to traditional float-based system
    // if (masterTickSystem && masterTickSystem->getTicksPerVinylRotation() > 0) {
    //     // Use tick-based position for more accurate tracking
    //     localPreciseCurrentFrame = tickToFramePosition(localCurrentTick);
    // }

    for (int i = 0; i < numOutputFrames; ++i) {
        if (!isPlaying.load()) {
            break;
        }

        // Use unified looping logic
        float originalFrame = localPreciseCurrentFrame;
        localPreciseCurrentFrame = applyLoopBoundaries(localPreciseCurrentFrame);

        // Handle special cases that require additional state changes
        if (originalFrame >= static_cast<float>(totalFrames) || originalFrame < 0.0f) {
            if (playOnceThenLoopSilently && !playedOnce) {
                playedOnce = true;
                if (!loop.load()) loop.store(true);
            } else if (!loop.load() && originalFrame >= static_cast<float>(totalFrames)) {
                // Non-looping sample reached the end
                isPlaying.store(false);

                // If this is a music track, notify the Java side for automatic next track
                if (isMusicTrack && audioEnginePtr) {
                    audioEnginePtr->notifyTrackCompleted();
                }

                break;
            }

            // LOG EXTENSIVE WRAPPING for debugging
            if (abs(originalFrame) > totalFrames * 2) { // More than 2 full rotations
                ALOGW("EXTENSIVE_WRAP: Frame %.1f → %.1f (totalFrames=%d, >2 rotations)",
                      originalFrame, localPreciseCurrentFrame, totalFrames);
            }
        }

        if (!isPlaying.load()) {
            break;
        }

        float fractionalTime = localPreciseCurrentFrame - std::floor(localPreciseCurrentFrame);
        int32_t baseFrameIndex = static_cast<int32_t>(std::floor(localPreciseCurrentFrame));
        int sincTableIndex = static_cast<int>(fractionalTime * SUBDIVISION_STEPS);
        sincTableIndex = std::min(sincTableIndex, SUBDIVISION_STEPS - 1);
        const std::vector<float>& coefficients = sincTable[sincTableIndex];
        int32_t kernelStartFrameIndex = baseFrameIndex - (NUM_TAPS / 2 - 1);

        for (int ch_out = 0; ch_out < outputStreamChannels; ++ch_out) {
            int srcChannel = ch_out % channels;
            float interpolatedSample = 0.0f;
            for (int k = 0; k < NUM_TAPS; ++k) {
                float sampleValue = getSampleAt(kernelStartFrameIndex + k, srcChannel);
                interpolatedSample += sampleValue * coefficients[k];
            }
            outputBuffer[i * outputStreamChannels + ch_out] += interpolatedSample * effectiveVolume;
        }

    // STEP 8: THE REAL FIX - Use double precision for scratch samples to prevent accumulation errors
    // Use the buffer-snapshotted engineRateMode to avoid mid-buffer behavior changes
    if (engineRateMode) {
            // For scratch samples, use double precision accumulation
            if (abs(doublePrecisionFrame - localPreciseCurrentFrame) > 1.0) {
                // Sync double precision with current position
                doublePrecisionFrame = localPreciseCurrentFrame;
            }

            // Advance with double precision using the buffer-corrected rate
            doublePrecisionFrame += (double)playbackRateToUse;
            localPreciseCurrentFrame = (float)doublePrecisionFrame;
        } else {
            // For non-scratch samples, use traditional float with buffer-corrected rate
            localPreciseCurrentFrame += playbackRateToUse;
        }

        // CRITICAL FIX: Apply loop boundaries after frame advancement
        // This prevents positions from advancing beyond loop boundaries between buffers
        float originalAdvancedFrame = localPreciseCurrentFrame;
        localPreciseCurrentFrame = applyLoopBoundaries(localPreciseCurrentFrame);

        // Sync double precision frame if loop boundary was applied
        if (engineRateMode && originalAdvancedFrame != localPreciseCurrentFrame) {
            doublePrecisionFrame = localPreciseCurrentFrame;
        }

        // Handle special state changes if loop boundary was crossed
        if (originalAdvancedFrame != localPreciseCurrentFrame) {
            if (playOnceThenLoopSilently && !playedOnce) {
                playedOnce = true;
                if (!loop.load()) loop.store(true);
            }
        }
    }

    // DEBUG: Check for position jumps OUTSIDE the loop (per buffer, not per sample)
    if (engineRateMode) {
        static int fixLogCounter = 0;
        static double lastDoubleFrame = 0.0;
        static float lastStoredFrame = 0.0f; // Keep this for actual store tracking
        
        double frameJump = doublePrecisionFrame - lastDoubleFrame;  // Both double precision

        if (fixLogCounter++ % 10 == 0) { // Log every 10 buffers
            // Get current stored frame for comparison (before store operation)
            float currentStoredFrame = preciseCurrentFrame.load();
            float storedFrameJump = currentStoredFrame - lastStoredFrame;
            float expectedJump = playbackRateToUse * numOutputFrames; // Expected jump per buffer

            // FIXED: Detect and correct for looping in jump calculation
            if (loop.load() && totalFrames > 0) {
                // Check if this looks like a loop wraparound (large negative jump)
                if (storedFrameJump < -(float)totalFrames * 0.5f) {
                    // This is a backward loop wraparound, adjust the calculation
                    storedFrameJump += (float)totalFrames;
                } else if (storedFrameJump > (float)totalFrames * 0.5f) {
                    // This is a forward loop wraparound, adjust the calculation
                    storedFrameJump -= (float)totalFrames;
                }
            }

            // Note: Removed verbose AudioPositionDebug and AudioJumpDebug for performance

            if (abs(frameJump) > abs(expectedJump) * 2.0 && abs(playbackRateToUse) < 2.0f) {
                ALOGE("POSITION JUMP DETECTED: DoubleJump=%.6f, StoredJump=%.6f, Expected=%.6f - investigating cause!",
                      frameJump, storedFrameJump, expectedJump);
            }

            lastStoredFrame = currentStoredFrame;
        }
        lastDoubleFrame = doublePrecisionFrame;
    }

    // Store position (using enhanced float system)
    float beforeStoreFrame = preciseCurrentFrame.load();

    // CRITICAL DEBUG: Check if position was modified between load and store
    float expectedLocalAdvancement = localPreciseCurrentFrame - beforeStoreFrame;
    float expectedBufferAdvancement = playbackRateToUse * numOutputFrames;

    // Note: Removed verbose STORE_DEBUG logging for performance

    // OPTIMIZED: Store position with minimal logging for performance
    preciseCurrentFrame.store(localPreciseCurrentFrame);

    // ===== ENHANCED POSITION CONTINUITY VERIFICATION =====
    // Real corruption detection based on actual stored frame continuity
    static float lastContinuityStoredFrame = 0.0f; // Unique name for continuity tracking
    static bool firstStore = true;
    
    if (!firstStore) {
        float actualAdvancement = localPreciseCurrentFrame - lastContinuityStoredFrame;
        float expectedAdvancement = expectedLocalAdvancement;
        float advancementRatio = expectedAdvancement != 0.0f ? (actualAdvancement / expectedAdvancement) : 0.0f;
        
        // CRITICAL: Detect if position jumped significantly beyond expected advancement
        if (abs(actualAdvancement - expectedAdvancement) > 50.0f) {
            ALOGE("POSITION_CONTINUITY_BREAK: LastStored=%.2f, CurrentStored=%.2f, ActualAdv=%.2f, ExpectedAdv=%.2f, Ratio=%.2f",
                  lastContinuityStoredFrame, localPreciseCurrentFrame, actualAdvancement, expectedAdvancement, advancementRatio);
        }
    }
    lastContinuityStoredFrame = localPreciseCurrentFrame;
    firstStore = false;

    // Note: Removed verbose ADVANCEMENT_DEBUG logging for performance

    // Note: Removed verbose POSITION_STORE_TRACKING logging for performance

    // CRITICAL FIX: Calculate stored frame jump using the ACTUAL newly stored frame
    // This fixes the 1-buffer delay bug in the AudioJumpDebug calculation!
    static float lastVerifiedStoredFrame = 0.0f; // Different name to avoid conflict
    float actualStoredJump = localPreciseCurrentFrame - lastVerifiedStoredFrame;

    // Apply loop wraparound correction to the ACTUAL stored jump
    if (loop.load() && totalFrames > 0) {
        if (actualStoredJump < -(float)totalFrames * 0.5f) {
            actualStoredJump += (float)totalFrames;
        } else if (actualStoredJump > (float)totalFrames * 0.5f) {
            actualStoredJump -= (float)totalFrames;
        }
    }

    // Note: Removed verbose CORRECTED_JUMP_DEBUG logging for performance

    lastVerifiedStoredFrame = localPreciseCurrentFrame;

    // Update the corruption tracking variable (declared in load section)
    // lastStoredFrame = localPreciseCurrentFrame; // This will be set in the load section
}

void AudioSample::loadFromMemory(const unsigned char* dataBuffer, size_t dataBufferSize, const std::string& identifier, uint64_t modificationTimestamp) {
    if (!sincTableInitialized) {
        precalculateSincTable();
    }
    this->filePath = identifier;
    isPlaying.store(false);
    preciseCurrentFrame.store(0.0f);
    currentTick.store(0);
    doublePrecisionFrame = 0.0;
    useEngineRateForPlayback_.store(false);
    playedOnce = false;
    loop.store(false);
    playOnceThenLoopSilently = false;

    ALOGI("AudioSample::loadFromMemory (User File): Attempting to load: %s, ModTime: %" PRIu64, identifier.c_str(), modificationTimestamp);

    if (!this->audioEnginePtr) { ALOGE("AudioSample::loadFromMemory (User File): AudioEngine pointer is null for %s!", identifier.c_str()); return; }

    std::string persistentCacheFilePath = this->audioEnginePtr->getPersistentCacheFilePath(identifier, modificationTimestamp);
    if (!persistentCacheFilePath.empty()) {
        if (loadFromPersistentCache(persistentCacheFilePath, modificationTimestamp, identifier)) {
            ALOGI("AudioSample::loadFromMemory (User File): Successfully loaded '%s' from persistent cache: %s", identifier.c_str(), persistentCacheFilePath.c_str());
            // Ensure stream sample rate and standardized vinyl length even for cached loads
            if (this->audioEnginePtr->getStreamSampleRate() > 0 && this->sampleRate != this->audioEnginePtr->getStreamSampleRate()) {
                ALOGW("AudioSample::loadFromMemory (User File): Cached sample rate %u for '%s' differs from stream rate %u. Resampling.", this->sampleRate, identifier.c_str(), this->audioEnginePtr->getStreamSampleRate());
                resampleDataTo(this->audioEnginePtr->getStreamSampleRate());
            }
            // CRITICAL: Standardize to 25 RPM base length so angle mapping is consistent across files
            standardizeToVinylLength();
            return;
        } else {
            ALOGI("AudioSample::loadFromMemory (User File): Persistent cache miss or stale for '%s' (Path: %s). Loading from memory buffer.", identifier.c_str(), persistentCacheFilePath.c_str());
        }
    } else {
        ALOGW("AudioSample::loadFromMemory (User File): Could not generate persistent cache file path for %s. Proceeding without persistent cache.", identifier.c_str());
    }

    audioData.clear(); totalFrames = 0; channels = 0; sampleRate = 0;
    bool success = false;

    ALOGI("AudioSample::loadFromMemory (User File): Decoding from memory buffer for identifier: %s, buffer size: %zu bytes", identifier.c_str(), dataBufferSize);

    if (!dataBuffer || dataBufferSize == 0) {
        ALOGE("AudioSample::loadFromMemory (User File): Data buffer is null or empty for USER FILE %s.", identifier.c_str());
        return;
    }

    std::string lowerIdentifier = identifier;
    std::transform(lowerIdentifier.begin(), lowerIdentifier.end(), lowerIdentifier.begin(), ::tolower);

    if (lowerIdentifier.length() > 4 && lowerIdentifier.substr(lowerIdentifier.length() - 4) == ".wav") {
        drwav wav;
        if (drwav_init_memory(&wav, dataBuffer, dataBufferSize, nullptr)) {
            channels = wav.channels; totalFrames = (int32_t)wav.totalPCMFrameCount; sampleRate = wav.sampleRate;
            audioData.resize(static_cast<size_t>(totalFrames) * channels);
            success = (drwav_read_pcm_frames_f32(&wav, totalFrames, audioData.data()) == static_cast<drwav_uint64>(totalFrames));
            drwav_uninit(&wav);
            if (success) ALOGI("AudioSample::loadFromMemory (User File): Successfully decoded WAV from memory: %s", identifier.c_str());
            else ALOGE("AudioSample::loadFromMemory (User File): Failed to read PCM frames for WAV from memory: %s", identifier.c_str());
        } else ALOGE("AudioSample::loadFromMemory (User File): Failed to initialize WAV from memory: %s", identifier.c_str());
    } else if (lowerIdentifier.length() > 4 && lowerIdentifier.substr(lowerIdentifier.length() - 4) == ".mp3") {
        drmp3_config config; drmp3_uint64 pcmFrameCount;
        float* pPcmFrames = drmp3_open_memory_and_read_pcm_frames_f32(dataBuffer, dataBufferSize, &config, &pcmFrameCount, nullptr);
        if (pPcmFrames) {
            channels = config.channels; sampleRate = config.sampleRate; totalFrames = (int32_t)pcmFrameCount;
            audioData.assign(pPcmFrames, pPcmFrames + (static_cast<size_t>(pcmFrameCount) * channels));
            drmp3_free(pPcmFrames, nullptr); success = true;
            ALOGI("AudioSample::loadFromMemory (User File): Successfully decoded MP3 from memory: %s", identifier.c_str());
        } else ALOGE("AudioSample::loadFromMemory (User File): Failed to initialize MP3 from memory: %s", identifier.c_str());
    } else {
        ALOGW("AudioSample::loadFromMemory (User File): Unknown file type for memory loading: %s. Attempting WAV, then MP3.", identifier.c_str());
        drwav wav_try;
        if (drwav_init_memory(&wav_try, dataBuffer, dataBufferSize, nullptr)) {
            channels = wav_try.channels; totalFrames = (int32_t)wav_try.totalPCMFrameCount; sampleRate = wav_try.sampleRate;
            audioData.resize(static_cast<size_t>(totalFrames) * channels);
            success = (drwav_read_pcm_frames_f32(&wav_try, totalFrames, audioData.data()) == static_cast<drwav_uint64>(totalFrames));
            drwav_uninit(&wav_try);
            if(success) ALOGI("AudioSample::loadFromMemory (User File): Successfully decoded (tried WAV) from memory: %s", identifier.c_str());
        }
        if(!success) {
            ALOGI("AudioSample::loadFromMemory (User File): WAV attempt failed for %s, trying MP3.", identifier.c_str());
            drmp3_config config_try; drmp3_uint64 pcmFrameCount_try;
            float* pPcmFrames_try = drmp3_open_memory_and_read_pcm_frames_f32(dataBuffer, dataBufferSize, &config_try, &pcmFrameCount_try, nullptr);
            if (pPcmFrames_try) {
                channels = config_try.channels; sampleRate = config_try.sampleRate; totalFrames = (int32_t)pcmFrameCount_try;
                audioData.assign(pPcmFrames_try, pPcmFrames_try + (static_cast<size_t>(pcmFrameCount_try) * channels));
                drmp3_free(pPcmFrames_try, nullptr); success = true;
                ALOGI("AudioSample::loadFromMemory (User File): Successfully decoded (tried MP3) from memory: %s", identifier.c_str());
            }
        }
        if(!success) ALOGE("AudioSample::loadFromMemory (User File): Failed to decode from memory, tried WAV and MP3: %s", identifier.c_str());
    }

    if (success) {
        ALOGI("AudioSample::loadFromMemory (User File): Decoded USER FILE from memory '%s' (Frames: %d, Ch: %d, SR: %u Hz)",
              this->filePath.c_str(), totalFrames, channels, sampleRate);

        if (this->audioEnginePtr->getStreamSampleRate() > 0) {
            uint32_t targetSr = this->audioEnginePtr->getStreamSampleRate();
            if (this->sampleRate != targetSr && targetSr > 0 && this->sampleRate > 0) {
                ALOGI("AudioSample::loadFromMemory (User File): Resampling user file '%s' from %u Hz to %u Hz", this->filePath.c_str(), this->sampleRate, targetSr);
                resampleDataTo(targetSr);
                ALOGI("AudioSample::loadFromMemory (User File): Resampling complete for user file '%s'. New frames: %d, new SR: %u", this->filePath.c_str(), this->totalFrames, this->sampleRate);
            } else {
                ALOGI("AudioSample::loadFromMemory (User File): No resampling needed for user file '%s' (Original SR: %u Hz, Target SR: %u Hz)", this->filePath.c_str(), this->sampleRate, targetSr);
            }
            
            // CRITICAL FIX: Automatically standardize to vinyl length for perfect sync
            standardizeToVinylLength();
            
            if (!persistentCacheFilePath.empty()) {
                saveToPersistentCache(persistentCacheFilePath, modificationTimestamp);
            }
        }
    } else {
        ALOGE("AudioSample::loadFromMemory (User File): Failed to decode USER FILE audio from memory for identifier '%s'", identifier.c_str());
        audioData.clear(); totalFrames = 0; channels = 0; sampleRate = 0;
    }
}

void AudioSample::resampleDataTo(uint32_t targetSampleRate) {
    ALOGI("AudioSample::resampleDataTo: Starting OPTIMIZED resampling for '%s'. Original SR: %u, Target SR: %u, Original Frames: %d",
          this->filePath.c_str(), this->sampleRate, targetSampleRate, this->totalFrames);

    if (this->sampleRate == 0) {
        ALOGE("AudioSample::resampleDataTo: Original sample rate is 0. Cannot resample.");
        return;
    }
    if (targetSampleRate == this->sampleRate || this->totalFrames == 0 || this->channels == 0) {
        ALOGI("AudioSample::resampleDataTo: No resampling needed or not possible (target SR same, zero frames/channels). Original SR: %u, Target SR: %u, Frames: %d, Channels: %d",
              this->sampleRate, targetSampleRate, this->totalFrames, this->channels);
        return;
    }

    double ratio = static_cast<double>(targetSampleRate) / this->sampleRate;
    int32_t newTotalFrames = static_cast<int32_t>(std::round(static_cast<double>(this->totalFrames) * ratio));

    ALOGI("AudioSample::resampleDataTo: OPTIMIZED resampling ratio: %f, newTotalFrames: %d", ratio, newTotalFrames);

    if (newTotalFrames == 0) {
        ALOGW("AudioSample::resampleDataTo: newTotalFrames is 0 after ratio calculation. Clearing audio data.");
        this->audioData.clear();
        this->totalFrames = 0;
        return;
    }

    std::vector<float> resampledAudioData;
    resampledAudioData.reserve(static_cast<size_t>(newTotalFrames) * this->channels);

    if (this->sampleRate == 44100 && targetSampleRate == 48000) {
        ALOGI("AudioSample::resampleDataTo: Using fast 44.1->48kHz path");
        
        const size_t oldDataSize = this->audioData.size();
        for (int32_t j = 0; j < newTotalFrames; ++j) {
            int32_t baseFrameIndex = (j * 147) / 160;
            int32_t remainder = (j * 147) % 160;
            float fractionalTime = static_cast<float>(remainder) / 160.0f;
            
            int32_t nextFrameIndex = std::min(baseFrameIndex + 1, this->totalFrames - 1);
            baseFrameIndex = std::max(0, std::min(baseFrameIndex, this->totalFrames - 1));

            for (int ch = 0; ch < this->channels; ++ch) {
                size_t baseIdx = static_cast<size_t>(baseFrameIndex) * this->channels + ch;
                size_t nextIdx = static_cast<size_t>(nextFrameIndex) * this->channels + ch;
                
                float sample1 = (baseIdx < oldDataSize) ? this->audioData[baseIdx] : 0.0f;
                float sample2 = (nextIdx < oldDataSize) ? this->audioData[nextIdx] : 0.0f;
                
                float interpolatedSample = sample1 + (sample2 - sample1) * fractionalTime;
                resampledAudioData.push_back(interpolatedSample);
            }
        }
    } else {
        ALOGI("AudioSample::resampleDataTo: Using general optimized path");
        
        const double stepSize = 1.0 / ratio;
        const size_t oldDataSize = this->audioData.size();
        
        for (int32_t j = 0; j < newTotalFrames; ++j) {
            double sourceFrameEquivalent = static_cast<double>(j) * stepSize;
            int32_t baseFrameIndex = static_cast<int32_t>(sourceFrameEquivalent);
            float fractionalTime = static_cast<float>(sourceFrameEquivalent - baseFrameIndex);
            
            int32_t nextFrameIndex = std::min(baseFrameIndex + 1, this->totalFrames - 1);
            baseFrameIndex = std::max(0, std::min(baseFrameIndex, this->totalFrames - 1));

            for (int ch = 0; ch < this->channels; ++ch) {
                size_t baseIdx = static_cast<size_t>(baseFrameIndex) * this->channels + ch;
                size_t nextIdx = static_cast<size_t>(nextFrameIndex) * this->channels + ch;
                
                float sample1 = (baseIdx < oldDataSize) ? this->audioData[baseIdx] : 0.0f;
                float sample2 = (nextIdx < oldDataSize) ? this->audioData[nextIdx] : 0.0f;
                
                float interpolatedSample = sample1 + (sample2 - sample1) * fractionalTime;
                resampledAudioData.push_back(interpolatedSample);
            }
        }
    }

    this->audioData = std::move(resampledAudioData);
    this->sampleRate = targetSampleRate;
    this->totalFrames = newTotalFrames;

    ALOGI("AudioSample::resampleDataTo: OPTIMIZED resampling successful for '%s'. New SR: %u, New Frames: %d",
          this->filePath.c_str(), this->sampleRate, this->totalFrames);
}

void AudioSample::standardizeToVinylLength() {
    // Target base length: 115,200 frames (25 RPM at 48kHz = 48000 * 60 / 25 = 115,200)
    const int32_t BASE_VINYL_FRAMES = 115200;
    
    if (this->totalFrames == 0 || this->channels == 0 || this->audioData.empty()) {
        ALOGE("AudioSample::standardizeToVinylLength: Cannot standardize empty or invalid audio data for '%s'", 
              this->filePath.c_str());
        return;
    }
    
    // Calculate the target length as the smallest multiple of BASE_VINYL_FRAMES that fits all audio
    int32_t vinylMultiplier = (this->totalFrames + BASE_VINYL_FRAMES - 1) / BASE_VINYL_FRAMES; // Ceiling division
    int32_t targetFrames = vinylMultiplier * BASE_VINYL_FRAMES;
    
    if (this->totalFrames == targetFrames) {
        ALOGI("AudioSample::standardizeToVinylLength: '%s' already exactly %d frames (%dx vinyl length). No standardization needed.", 
              this->filePath.c_str(), targetFrames, vinylMultiplier);
        return;
    }
    
    ALOGI("AudioSample::standardizeToVinylLength: Standardizing '%s' from %d frames to %d frames (%dx vinyl length)", 
          this->filePath.c_str(), this->totalFrames, targetFrames, vinylMultiplier);
    
    std::vector<float> standardizedData;
    standardizedData.reserve(static_cast<size_t>(targetFrames) * this->channels);
    
    if (this->totalFrames < BASE_VINYL_FRAMES) {
        // Short file: Loop as many complete times as possible, then pad with silence
        int32_t completeLoops = BASE_VINYL_FRAMES / this->totalFrames;
        int32_t remainingFrames = BASE_VINYL_FRAMES - (completeLoops * this->totalFrames);
        
        ALOGI("AudioSample::standardizeToVinylLength: Short file strategy - %d complete loops + %d silence frames", 
              completeLoops, remainingFrames);
        
        // Add complete loops of the original audio
        for (int32_t loop = 0; loop < completeLoops; ++loop) {
            standardizedData.insert(standardizedData.end(), this->audioData.begin(), this->audioData.end());
        }
        
        // Add silence padding to reach exactly BASE_VINYL_FRAMES
        for (int32_t frame = 0; frame < remainingFrames; ++frame) {
            for (int ch = 0; ch < this->channels; ++ch) {
                standardizedData.push_back(0.0f); // Silence
            }
        }
        
    } else {
        // Long file: Copy all original audio, then pad with silence to reach next vinyl multiple
        int32_t silenceFramesNeeded = targetFrames - this->totalFrames;
        
        ALOGI("AudioSample::standardizeToVinylLength: Long file strategy - preserve all %d frames + %d silence frames", 
              this->totalFrames, silenceFramesNeeded);
        
        // Copy all original audio (NO TRUNCATION EVER!)
        standardizedData.assign(this->audioData.begin(), this->audioData.end());
        
        // Add silence padding to reach the next vinyl multiple
        for (int32_t frame = 0; frame < silenceFramesNeeded; ++frame) {
            for (int ch = 0; ch < this->channels; ++ch) {
                standardizedData.push_back(0.0f); // Silence
            }
        }
    }
    
    // Replace the original audio data
    this->audioData = std::move(standardizedData);
    this->totalFrames = targetFrames;
    
    ALOGI("AudioSample::standardizeToVinylLength: Successfully standardized '%s' to %d frames (%dx%d vinyl units, %d total samples)", 
          this->filePath.c_str(), this->totalFrames, vinylMultiplier, BASE_VINYL_FRAMES, static_cast<int>(this->audioData.size()));
}

// Tick-based position conversion methods
float AudioSample::tickToFramePosition(int64_t tick) const {
    if (!audioEnginePtr) return 0.0f;

    // Get the master tick system from the audio engine
    auto* masterTickSystem = audioEnginePtr->getMasterTickSystem();
    if (!masterTickSystem) return 0.0f;

    uint64_t ticksPerRotation = masterTickSystem->getTicksPerVinylRotation();
    if (ticksPerRotation == 0 || totalFrames == 0) return 0.0f;

    // Convert tick to frame position within the sample
    // One full rotation = one full sample playthrough
    uint64_t tickInRotation = tick % ticksPerRotation;
    return (float)(tickInRotation * totalFrames) / (float)ticksPerRotation;
}

int64_t AudioSample::framePositionToTick(float framePos) const {
    if (!audioEnginePtr) return 0;

    // Get the master tick system from the audio engine
    auto* masterTickSystem = audioEnginePtr->getMasterTickSystem();
    if (!masterTickSystem) return 0;

    uint64_t ticksPerRotation = masterTickSystem->getTicksPerVinylRotation();
    if (ticksPerRotation == 0 || totalFrames == 0) return 0;

    // Convert frame position to tick
    // Ensure framePos is within bounds
    float clampedFramePos = std::max(0.0f, std::min(framePos, (float)totalFrames));
    return (int64_t)((clampedFramePos / (float)totalFrames) * (float)ticksPerRotation);
}

// Unified looping logic - handles all loop boundary cases consistently
float AudioSample::applyLoopBoundaries(float currentFrame) const {
    // Handle out-of-bounds cases
    if (currentFrame >= static_cast<float>(totalFrames) || currentFrame < 0.0f) {
        if (playOnceThenLoopSilently && !playedOnce) {
            // Special case: play once then loop silently
            return 0.0f; // Reset to beginning, caller should set playedOnce=true and loop=true
        } else if (loop.load()) {
            // Standard looping case
            if (totalFrames > 0) {
                // Use fmodf for consistent float-based wrapping
                float wrappedFrame = fmodf(currentFrame, static_cast<float>(totalFrames));
                if (wrappedFrame < 0.0f) {
                    wrappedFrame += static_cast<float>(totalFrames);
                }
                return wrappedFrame;
            } else {
                return 0.0f;
            }
        } else {
            // No looping - clamp to valid range or signal stop
            if (currentFrame >= static_cast<float>(totalFrames)) {
                return static_cast<float>(totalFrames - 1); // Clamp to last frame
            } else {
                return 0.0f; // Clamp to first frame
            }
        }
    }

    // Frame is within bounds, return as-is
    return currentFrame;
}
