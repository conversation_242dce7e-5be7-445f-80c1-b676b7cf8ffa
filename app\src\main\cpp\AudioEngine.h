#pragma once
#include <atomic>
#include <memory>
#include <vector>
#include <string>
#include <map>
#include <list>
#include <mutex>
#include <cmath>
#include <oboe/Oboe.h>
#include <android/asset_manager.h>
#include <jni.h>

class AudioSample;
class VinylTracker;

// Single source of truth slipmat physics engine
class SlipmatPhysics {
private:
    std::atomic<float> dampingFactor_{0.14f};
    std::atomic<float> abruptness_{2.5f};
    std::atomic<float> baseFriction_{0.25f};
    std::atomic<float> currentSpeed_{0.0f};
    std::atomic<float> targetSpeed_{0.0f};
    std::atomic<bool> isManualControl_{false};

    // Audio-rate timing (much more precise than 60fps)
    static constexpr float AUDIO_SAMPLE_RATE = 48000.0f;
    static constexpr float AUDIO_DT = 1.0f / AUDIO_SAMPLE_RATE; // ~0.0208ms vs 16.67ms for 60fps

public:
    SlipmatPhysics() {
        // Initialize with motor speed as target
        targetSpeed_.store(1.0f);
        currentSpeed_.store(1.0f);
    }

    // Update slipmat parameters from UI
    void setDampingFactor(float damping) {
        float clampedDamping = std::clamp(damping, 0.0f, 1.0f);  // TUNED RANGE: 0-100% precise control
        dampingFactor_.store(clampedDamping);
        // Note: Can't use ALOGI in header, will add logging in AudioEngine.cpp
    }
    void setAbruptness(float abrupt) {
        float clampedAbrupt = std::clamp(abrupt, 1.0f, 15.0f);  // TUNED RANGE: 1-15 precise control
        abruptness_.store(clampedAbrupt);
        // Note: Can't use ALOGI in header, will add logging in AudioEngine.cpp
    }
    void setBaseFriction(float friction) {
        float clampedFriction = std::clamp(friction, 1.0f, 15.0f);  // TUNED RANGE: 1-15 precise control
        baseFriction_.store(clampedFriction);
        // Note: Can't use ALOGI in header, will add logging in AudioEngine.cpp
    }

    // Control methods
    void setManualControl(bool manual, float speed = 0.0f) {
        isManualControl_.store(manual);
        if (manual) {
            currentSpeed_.store(speed);
        }
        // When switching from manual to motor, preserve current speed as starting point
    }

    void setCurrentSpeed(float speed) {
        currentSpeed_.store(speed);
    }

    void setTargetSpeed(float target) {
        targetSpeed_.store(target);
    }

    // Physics calculation - called at audio rate (48kHz) for smooth transitions
    float calculateNextSpeed() {
        if (isManualControl_.load()) {
            return currentSpeed_.load(); // Manual control - no physics
        }

        float current = currentSpeed_.load();
        float target = targetSpeed_.load();
        float speedDifference = target - current;

        // Same physics as visual system but at 48kHz precision
        float userDamping = dampingFactor_.load();
        float abruptnessB = abruptness_.load();
        float baseFrictionC = baseFriction_.load();

        // Calculate how close we are to target (0 = far, 1 = at target)
        float maxSpeedDiff = std::abs(target) + 10.0f;
        float normalizedCloseness = 1.0f - std::clamp(std::abs(speedDifference) / maxSpeedDiff, 0.0f, 1.0f);

        // PERFECTED exponential curve: instant drag engagement + exponential snap
        // Base friction provides instant "drag" level
        float instantDrag = baseFrictionC * 2.0f; // Double base friction for instant engagement

        // Exponential acceleration that "snaps" when close to target
        float exponentialAcceleration = std::pow(abruptnessB, normalizedCloseness);
        float exponentialComponent = (exponentialAcceleration / abruptnessB) * (1.0f + normalizedCloseness * 3.0f);

        // Combine: instant drag + exponential snap
        float totalAccelerationFactor = instantDrag + exponentialComponent;

        // Apply damping at audio rate (much smoother than 60fps)
        // BRILLIANT USER INSIGHT: Scale physics to 60Hz equivalent for realistic response
        // while keeping audio precision at 48kHz
        float physicsTimeScale = 60.0f; // Make physics 60x more responsive
        float dampedAcceleration = totalAccelerationFactor * userDamping * AUDIO_DT * physicsTimeScale;

        // CRITICAL FIX: Much tighter snap-to-target to prevent micro-drift accumulation
        // When very close to target, snap to avoid infinite approach and sync drift
        float snapThreshold = 0.001f; // Much tighter than 0.01f
        if (std::abs(speedDifference) < snapThreshold) {
            currentSpeed_.store(target);
            return target;
        }

        // Calculate new speed
        float newSpeed = current + speedDifference * std::clamp(dampedAcceleration, 0.0f, 1.0f);
        
        // ADDITIONAL FIX: Prevent micro-oscillations around target
        // If we're getting very close and the change is tiny, just snap to target
        float changeAmount = std::abs(newSpeed - current);
        if (changeAmount < 0.0001f && std::abs(newSpeed - target) < 0.005f) {
            currentSpeed_.store(target);
            return target;
        }
        
        currentSpeed_.store(newSpeed);

        return newSpeed;
    }

    float getCurrentSpeed() const { return currentSpeed_.load(); }
    float getTargetSpeed() const { return targetSpeed_.load(); }
    bool isManualControl() const { return isManualControl_.load(); }

    // Getter methods for debugging parameter values
    float getDampingFactor() const { return dampingFactor_.load(); }
    float getAbruptness() const { return abruptness_.load(); }
    float getBaseFriction() const { return baseFriction_.load(); }
};

// Callback-based synchronization data for VinylTracker
struct CallbackSyncData {
    std::atomic<uint64_t> masterTick{0};
    std::atomic<float> audioFrame{0.0f};
    std::atomic<bool> dataReady{false};
    std::atomic<bool> needsSync{false};
};

// Master tick system for high-precision timing
class MasterTickSystem {
private:
    std::atomic<uint64_t> masterTick_{0};
    std::atomic<uint32_t> sampleRate_{48000};
    std::atomic<uint64_t> ticksPerVinylRotation_{0}; // Will be set based on sample length

public:
    MasterTickSystem() = default;

    // Called from audio callback - most precise timing source
    void incrementTick(uint32_t numFrames = 1) {
        masterTick_.fetch_add(numFrames);
    }

    uint64_t getCurrentTick() const {
        return masterTick_.load();
    }

    void setSampleRate(uint32_t rate) {
        sampleRate_.store(rate);
    }

    uint32_t getSampleRate() const {
        return sampleRate_.load();
    }

    void setTicksPerVinylRotation(uint64_t ticks) {
        ticksPerVinylRotation_.store(ticks);
    }

    uint64_t getTicksPerVinylRotation() const {
        return ticksPerVinylRotation_.load();
    }

    // Convert ticks to vinyl angle (0-360 degrees) - PRECISION FIX
    float ticksToVinylAngle(uint64_t ticks) const {
        uint64_t ticksPerRotation = ticksPerVinylRotation_.load();
        if (ticksPerRotation == 0) return 0.0f;

        uint64_t ticksInRotation = ticks % ticksPerRotation;
        // Use double precision to prevent integer overflow and precision loss
        return (float)((double)ticksInRotation * 360.0 / (double)ticksPerRotation);
    }

    // Convert ticks to unwrapped angle (can exceed 360) - PRECISION FIX
    float ticksToUnwrappedAngle(uint64_t ticks) const {
        uint64_t ticksPerRotation = ticksPerVinylRotation_.load();
        if (ticksPerRotation == 0) return 0.0f;

        // Use double precision to prevent integer overflow and precision loss
        return (float)((double)ticks * 360.0 / (double)ticksPerRotation);
    }

    // Convert ticks to time in seconds
    double ticksToSeconds(uint64_t ticks) const {
        return (double)ticks / (double)sampleRate_.load();
    }

    // Convert angle delta to tick delta
    uint64_t angleDeltaToTicks(float angleDelta) const {
        uint64_t ticksPerRotation = ticksPerVinylRotation_.load();
        if (ticksPerRotation == 0) return 0;

        return (uint64_t)((angleDelta / 360.0f) * (float)ticksPerRotation);
    }

    void reset() {
        masterTick_.store(0);
    }
};

class AudioEngine : public oboe::AudioStreamCallback {
public:
    // Constructor and Destructor
    AudioEngine();
    ~AudioEngine();

    std::atomic<float> platterTargetPlaybackRate_{1.0f};

    const float MOVEMENT_THRESHOLD = 0.001f;
    float degreesPerFrameForUnityRate_ = 1.25f;  // STEP 1: Fix 2:1 sensitivity ratio

    AAssetManager* appAssetManager_ = nullptr;
    std::shared_ptr<AudioSample> activePlatterSample_;
    std::shared_ptr<AudioSample> activeMusicSample_;
    // Default maximum platter speed (can now be adjusted at runtime via JNI)
    static constexpr float DEFAULT_MAX_PLATTER_SPEED = 1.5f;
    static constexpr float SPEED_LIMIT_SMOOTHING = 0.85f;
    float previousLimitedSpeed = 1.0f;
    std::atomic<float> maxPlatterSpeed_{DEFAULT_MAX_PLATTER_SPEED};
    std::atomic<bool> introSampleActive_{false}; // Guard to prevent duplicate intro triggering
    
    float limitPlatterSpeed(float requestedSpeed);
    void setMaxPlatterSpeed(float newMax);
    float getMaxPlatterSpeed() const { return maxPlatterSpeed_.load(); }
    bool tryBeginIntro();
    bool init(AAssetManager* mgr);
    void release();
    oboe::Result startStream();
    oboe::Result stopStream();

    void stopMusicTrackInternal();
    void setPlatterFaderVolumeInternal(float volume);
    void setMusicMasterVolumeInternal(float volume);
    void scratchPlatterActiveInternal(bool isActiveTouch, float angleDeltaOrRateFromViewModel);
    void releasePlatterTouchInternal();

    void engineLoadUserMusicTrack(const std::string& uriString, int fd, long offset, long length, uint64_t modificationTimestamp);
    void engineLoadAssetPlatter(const std::string& assetPath, uint64_t appVersionCode);
    void engineLoadAssetMusic(const std::string& assetPath, uint64_t appVersionCode);
    void enginePlayIntroAndLoopOnPlatter(const std::string& assetPath, uint64_t appVersionCode);
    void engineLoadUserPlatterSample(const std::string& uriString, int fd, long offset, long length, uint64_t modificationTimestamp);

    oboe::DataCallbackResult onAudioReady(oboe::AudioStream* stream, void* audioData, int32_t numFrames) override;
    void onErrorBeforeClose(oboe::AudioStream *stream, oboe::Result error) override;
    void onErrorAfterClose(oboe::AudioStream *stream, oboe::Result error) override;
    std::string getPersistentCacheFilePath(const std::string& originalPathOrUri, uint64_t modificationTimestamp);
    bool isPlatterTouched() const;
    uint32_t getStreamSampleRate() const;
    std::vector<std::string> internalAssetPlatterSamplePaths_;
    std::vector<std::string> internalAssetMusicTrackPaths_;
    std::atomic<int> currentPlatterSampleIndex_;
    std::atomic<int> currentMusicTrackIndex_;
    std::string baseCachePath_;

    void setDegreesPerFrameForUnityRateInternal(float degrees);

    // JNI public wrappers
    void loadAssetPlatterSample(const std::string& assetPath, uint64_t appVersionCode);
    void setScratchPlatterActive(bool active, float velocity);
    
    // VinylTracker public methods
    void startVinylTracking();
    void stopVinylTracking();
    float getCurrentVinylAngle();
    bool isVinylTracking();
    
    // Audio-synchronized vinyl tracking methods
    float getPlatterAudioPosition();
    float getPlatterTotalFrames();
    float getUnwrappedVinylAngle();
    void resetVinylAngle();
    float getTotalRotations();

    // Master tick system access
    MasterTickSystem* getMasterTickSystem() { return &masterTickSystem_; }
    uint64_t getCurrentMasterTick() const { return masterTickSystem_.getCurrentTick(); }
    float getCurrentTickBasedVinylAngle() const;
    // Returns the current audio angle based on preciseCurrentFrame (0-360)
    float getCurrentAudioAngleDegrees() const;
    void resetMasterTicks();

    // Callback-based synchronization for VinylTracker
    CallbackSyncData* getCallbackSyncData() { return &callbackSyncData_; }
    void requestVinylSync(); // Called by VinylTracker to request sync

    // JNI callback setup for track completion
    void setJavaCallback(JavaVM* jvm, jobject mainActivityObj);
    void notifyTrackCompleted(); // Public method to notify Java when track completes

    // Slipmat physics control methods
    void setSlipmatDamping(float damping);
    void setSlipmatAbruptness(float abruptness);
    void setSlipmatBaseFriction(float baseFriction);
    void setSlipmatCurrentSpeed(float speed);
    float getCurrentSlipmatSpeed() const;
    float getTargetSlipmatSpeed() const;
private:
    std::shared_ptr<oboe::AudioStream> audioStream_;
    uint32_t streamSampleRate_ = 0;
    std::atomic<float> platterFaderVolume_;
    std::atomic<float> generalMusicVolume_;
    std::atomic<bool> isFingerDownOnPlatter_;
    
    // Minimal click prevention with immediate DJ-style response
    float lastPlatterVolume_ = 0.0f;
    float lastMusicVolume_ = 0.9f;

    // Holdoff timers (in frames) to temporarily disable motor positioning
    // - motorPositionHoldoffFrames_: engaged on finger release to avoid immediate corrections while states settle
    // - faderActivityHoldoffFrames_: engaged when platter volume changes significantly to avoid corrections during cuts
    std::atomic<int> motorPositionHoldoffFrames_{0};
    std::atomic<int> faderActivityHoldoffFrames_{0};
    // Pending control updates (UI threads set; applied in audio callback)
    std::atomic<bool> pendingSetManual_{false};
    std::atomic<float> pendingManualRate_{0.0f};
    std::atomic<bool> pendingSetMotor_{false};
    std::atomic<float> pendingMotorCurrent_{0.0f};
    std::atomic<float> pendingMotorTarget_{1.0f};
    
    static const size_t MAX_CACHED_SAMPLES_PER_TYPE = 2;
    std::map<std::string, std::shared_ptr<AudioSample>> platterSampleCache_;
    std::list<std::string> platterSampleLru_;
    std::map<std::string, std::shared_ptr<AudioSample>> musicTrackCache_;
    std::list<std::string> musicTrackLru_;
    std::mutex cacheMutex_;
    void manageCache(std::map<std::string, std::shared_ptr<AudioSample>>& cache,
                     std::list<std::string>& lruList,
                     const std::string& key,
                     std::shared_ptr<AudioSample> sampleToCache);
    void updateLru(std::list<std::string>& lruList, const std::string& key);

    // Helper method for tick system setup
    void setupTicksPerVinylRotation();

    // VinylTracker member
    std::unique_ptr<VinylTracker> vinylTracker_;

    // Master tick system for high-precision timing
    MasterTickSystem masterTickSystem_;

    // Callback-based synchronization data
    CallbackSyncData callbackSyncData_;

    // JNI callback members for track completion
    JavaVM* javaVM_ = nullptr;
    jobject mainActivityGlobalRef_ = nullptr;
    jmethodID onTrackCompletedMethodID_ = nullptr;

    // Single source of truth slipmat physics engine
    SlipmatPhysics slipmatPhysics_;
};
