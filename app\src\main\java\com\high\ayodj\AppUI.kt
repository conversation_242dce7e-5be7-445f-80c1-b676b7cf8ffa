package com.high.ayodj

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.GestureCancellationException
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.unit.sp
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.PointerId
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.foundation.shape.RoundedCornerShape
import com.high.ayodj.monetization.ui.TrackListUI
import com.high.ayodj.ui.theme.FromscratchTheme
import kotlin.math.PI
import kotlin.math.atan2
import kotlin.math.pow
import kotlin.math.roundToInt
import kotlin.math.sqrt
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension

@Composable
fun DjApp(viewModel: AppViewModel, billingClientWrapper: BillingClientWrapper) {
    FromscratchTheme {
        when (viewModel.currentScreen) {
            AppScreen.Loading -> LoadingScreen()
            AppScreen.Main -> MainDJScreen(viewModel = viewModel, billingClientWrapper = billingClientWrapper)
        }

        // Track List Dialog
        if (viewModel.showTrackList) {
            TrackListDialog(viewModel = viewModel)
        }
    }
}

@Composable
fun LoadingScreen() {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp

    Box(modifier = Modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = R.drawable.loadbg),
            contentDescription = "Loading Background",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        Column(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(bottom = 50.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = R.drawable.ayodjlogo),
                contentDescription = "AYO DJ Logo",
                modifier = Modifier.width(screenWidth * 0.8f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            CircularProgressIndicator(color = Color.White)
        }
        Text(
            text = "Hold both buttons for Settings",
            color = Color.LightGray.copy(alpha = 0.7f),
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(horizontal = 32.dp)
                .padding(bottom = 24.dp)
        )
    }
}

@Composable
fun MainDJScreen(viewModel: AppViewModel, billingClientWrapper: BillingClientWrapper) {
    val button1InteractionSource = remember { MutableInteractionSource() }
    val isButton1Pressed by button1InteractionSource.collectIsPressedAsState()
    val button2InteractionSource = remember { MutableInteractionSource() }
    val isButton2Pressed by button2InteractionSource.collectIsPressedAsState()

    val configuration = LocalConfiguration.current
    val screenWidthDp = configuration.screenWidthDp.dp
    val controlsBuffer = 10.dp
    val baseFaderHeight = 60.dp
    val faderHeight = baseFaderHeight * 1.3f
    val originalButtonHeight = screenWidthDp * 0.072f * 2.0f
    val originalButtonWidth = originalButtonHeight * 0.72f
    val newButtonHeight = originalButtonHeight * 1.5f
    val newButtonWidth = originalButtonWidth * 1.5f

    // Drive UI angles with vsync to reduce visual lag (disabled during touch to avoid conflicts)
    LaunchedEffect(Unit) {
        while (true) {
            withFrameNanos { frameTimeNanos ->
                // Only update via vsync when NOT touching to avoid dual-loop conflicts
                if (!viewModel.isPlatterTouched) {
                    viewModel.refreshAngles(frameTimeNanos)
                }
            }
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        if (viewModel.showSignInPrompt) {
            SignInPrompt(viewModel = viewModel)
        }
        Image(
            painter = painterResource(id = R.drawable.bg),
            contentDescription = "Background",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )

        PlatterView(
            vinylAngle = viewModel.vinylAngle,
            visualPlatterAngle = viewModel.visualPlatterAngle,
            onPress = viewModel::onPlatterTouchDown,
            onRelease = viewModel::onPlatterTouchUp,
            onDrag = viewModel::onPlatterDrag,
            onPressWithAngle = viewModel::onPlatterTouchDownWithAngle,  // STEP 6: Angular compensation
            contentScaleFactor = 4.0f,
            screenWidthDp = screenWidthDp,
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = newButtonHeight * 2 + faderHeight + controlsBuffer * 3 + controlsBuffer)
        )

        // Trial/Expired status banner at top
        val trialManager = viewModel.getTrialManager()
        val subscriptionStatus by viewModel.subscriptionStatus.collectAsState()
        
        // Show banner for TRIAL (active) or FREE (expired trial)
        if (subscriptionStatus == com.high.ayodj.monetization.SubscriptionStatus.TRIAL || 
            subscriptionStatus == com.high.ayodj.monetization.SubscriptionStatus.FREE) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                com.high.ayodj.monetization.ui.TrialStatusBanner(
                    trialManager = trialManager,
                    subscriptionStatus = subscriptionStatus,
                    onUpgradeClicked = { viewModel.showUpgradePrompt() }
                )
            }
        }

        ConstraintLayout(modifier = Modifier.fillMaxSize()) {
            val (fader, button1, button2, trackListButton) = createRefs()

            CustomFader(
                volume = viewModel.platterFaderVolume,
                onVolumeChange = viewModel::onPlatterFaderVolumeChange,
                faderAreaHeight = faderHeight,
                modifier = Modifier
                    .constrainAs(fader) {
                        bottom.linkTo(parent.bottom, margin = controlsBuffer)
                        start.linkTo(parent.start, margin = controlsBuffer)
                        end.linkTo(parent.end, margin = controlsBuffer)
                        width = Dimension.fillToConstraints
                    }
                    .height(faderHeight)
            )

            Box(
                modifier = Modifier
                    .constrainAs(button1) {
                        bottom.linkTo(button2.top, margin = controlsBuffer / 2)
                        end.linkTo(fader.end)
                    }
                    .size(width = newButtonWidth, height = newButtonHeight)
                    .pointerInput("button1") { // MULTI-TOUCH FIX: Unique key for button1
                        detectTapGestures(
                            onPress = { offset ->
                                viewModel.onButtonPressStateChange(buttonId = 1, isPressed = true)
                                val press = PressInteraction.Press(offset)
                                button1InteractionSource.emit(press)
                                try {
                                    if (tryAwaitRelease()) button1InteractionSource.emit(PressInteraction.Release(press))
                                    else button1InteractionSource.emit(PressInteraction.Cancel(press))
                                } catch (_: GestureCancellationException) {
                                    button1InteractionSource.emit(PressInteraction.Cancel(press))
                                } finally {
                                    viewModel.onButtonPressStateChange(buttonId = 1, isPressed = false)
                                }
                            },
                            onTap = { viewModel.handleButton1Press() }
                        )
                    }
            ) {
                // Show loading LED glow when loading platter samples, otherwise normal button states
                val loadingState = viewModel.audioLoadingState
                val isLoadingPlatterSample = loadingState is AudioLoadingState.Loading &&
                    loadingState.isBackgroundOperation &&
                    (loadingState.message.contains("platter sample", ignoreCase = true) ||
                     loadingState.message.contains("Initializing audio", ignoreCase = true))

                val buttonDrawable = when {
                    isLoadingPlatterSample -> R.drawable.r1b1_loading
                    isButton1Pressed -> R.drawable.r1b1p
                    else -> R.drawable.r1b1
                }

                Image(
                    painter = painterResource(id = buttonDrawable),
                    contentDescription = "Button 1",
                    contentScale = ContentScale.Fit,
                    modifier = Modifier.fillMaxSize()
                )
            }

            Box(
                modifier = Modifier
                    .constrainAs(button2) {
                        bottom.linkTo(fader.top, margin = controlsBuffer)
                        end.linkTo(fader.end)
                    }
                    .size(width = newButtonWidth, height = newButtonHeight)
                    .pointerInput("button2") { // MULTI-TOUCH FIX: Unique key for button2
                        detectTapGestures(
                            onPress = { offset ->
                                viewModel.onButtonPressStateChange(buttonId = 2, isPressed = true)
                                val press = PressInteraction.Press(offset)
                                button2InteractionSource.emit(press)
                                try {
                                    if (tryAwaitRelease()) button2InteractionSource.emit(PressInteraction.Release(press))
                                    else button2InteractionSource.emit(PressInteraction.Cancel(press))
                                } catch (_: GestureCancellationException) {
                                    button2InteractionSource.emit(PressInteraction.Cancel(press))
                                } finally {
                                    viewModel.onButtonPressStateChange(buttonId = 2, isPressed = false)
                                }
                            },
                            onTap = { viewModel.handleButton2Press() },
                            onDoubleTap = { viewModel.handleButton2DoublePress() }
                        )
                    }
            ) {
                // Show loading LED glow when loading music tracks, otherwise normal button states
                val loadingState = viewModel.audioLoadingState
                val isLoadingMusicTrack = loadingState is AudioLoadingState.Loading &&
                    loadingState.isBackgroundOperation &&
                    (loadingState.message.contains("Loading track", ignoreCase = true) ||
                     loadingState.message.contains("Loading next track", ignoreCase = true))

                val buttonDrawable = when {
                    isLoadingMusicTrack -> R.drawable.r1b2_loading
                    isButton2Pressed -> R.drawable.r1b2p
                    else -> R.drawable.r1b2
                }

                Image(
                    painter = painterResource(id = buttonDrawable),
                    contentDescription = "Button 2",
                    contentScale = ContentScale.Fit,
                    modifier = Modifier.fillMaxSize()
                )
            }

            // New Track Notification FAB (bottom-right corner with rotating emojis)
            if (viewModel.hasNewTrackNotification && !viewModel.isMusicPlaying) {
                FloatingActionButton(
                    onClick = { viewModel.onNewTrackNotificationClicked() },
                    modifier = Modifier
                        .constrainAs(trackListButton) {
                            bottom.linkTo(parent.bottom, margin = 17.dp)
                            end.linkTo(parent.end, margin = 12.dp)
                        },
                    containerColor = MaterialTheme.colorScheme.secondary
                ) {
                    Text(
                        text = viewModel.currentNotificationEmoji,
                        fontSize = 24.sp,
                        color = MaterialTheme.colorScheme.onSecondary
                    )
                }
            }
        }

        // Error overlay
        if (viewModel.audioLoadingState is AudioLoadingState.Error) {
            ErrorOverlay(
                error = viewModel.audioLoadingState as AudioLoadingState.Error,
                onDismiss = { /* Handle error dismissal */ }
            )
        }

        // Loading overlay - only show for non-background operations (critical loading)
        if (viewModel.audioLoadingState is AudioLoadingState.Loading) {
            val loadingState = viewModel.audioLoadingState as AudioLoadingState.Loading
            if (!loadingState.isBackgroundOperation) {
                LoadingOverlay(
                    message = loadingState.message
                )
            }
            // Background loading operations now show LED glow on Button 1 instead of overlay
        }

        // Settings dialog
        if (viewModel.showSettingsDialog) {
            SettingsDialog(
                viewModel = viewModel,
                onDismiss = { viewModel.closeSettingsDialog() }
            )
        }

        // Sound loading pages
        if (viewModel.showPlatterSampleLoadPage) {
            LoadSoundsPage(
                viewModel = viewModel,
                soundType = SoundType.PLATTER_SAMPLE,
                onDismiss = { viewModel.showPlatterSampleLoadPage = false }
            )
        }

        if (viewModel.showMusicTrackLoadPage) {
            LoadSoundsPage(
                viewModel = viewModel,
                soundType = SoundType.MUSIC_TRACK,
                onDismiss = { viewModel.showMusicTrackLoadPage = false }
            )
        }

        // Subscribe popup
        if (viewModel.showSubscribePopup) {
            val context = LocalContext.current
            PurchaseOptionsPopup(
                onDismiss = { viewModel.showSubscribePopup = false },
                onUploadAccessPurchase = {
                    viewModel.purchaseUploadAccess(context as android.app.Activity, billingClientWrapper)
                    viewModel.showSubscribePopup = false
                },
                onPremiumSubscription = {
                    viewModel.purchasePremiumSubscription(context as android.app.Activity, billingClientWrapper)
                    viewModel.showSubscribePopup = false
                }
            )
        }
        
        // Trial info popup (shown on first launch)
        if (viewModel.showTrialInfoPopup) {
            val context = LocalContext.current
            com.high.ayodj.monetization.ui.TrialInfoDialog(
                trialManager = viewModel.getTrialManager(),
                onDismiss = { viewModel.dismissTrialInfo() },
                onUpgradeClicked = {
                    // Default to premium subscription for trial upgrades
                    viewModel.purchasePremiumSubscription(context as android.app.Activity, billingClientWrapper)
                    viewModel.upgradeFromTrial()
                }
            )
        }
    }
}

@Composable
fun PlatterView(
    vinylAngle: Float,
    visualPlatterAngle: Float,
    onPress: () -> Unit,
    onRelease: () -> Unit,
    onDrag: (angleDelta: Float, radialFactor: Float) -> Unit,
    onPressWithAngle: (fingerAngle: Float) -> Unit = { },  // STEP 4: Angular compensation callback
    contentScaleFactor: Float,
    screenWidthDp: Dp,
    modifier: Modifier = Modifier
) {
    // Note: Removed verbose PlatterView composable logging for performance
    var previousAngle by remember { mutableFloatStateOf(0f) }
    var localCenterForDragCalc by remember { mutableStateOf(Offset.Zero) }
    var touchRadius by remember { mutableFloatStateOf(0f) }  // STEP 5: Track touch radius for compensation
    val density = LocalDensity.current
    var platterActualRenderSizePx by remember { mutableStateOf(IntSize.Zero) }
    // Strict multi-touch isolation: only the pointer that starts the drag can control the platter
    var activePointerId by remember { mutableStateOf<PointerId?>(null) }

    Box(
        modifier = modifier
            .aspectRatio(1f)
            .onSizeChanged {
                platterActualRenderSizePx = it
                android.util.Log.i("PlatterView", "onSizeChanged: platterActualRenderSizePx = $it")
            }
            .graphicsLayer {
                scaleX = contentScaleFactor
                scaleY = contentScaleFactor
                transformOrigin = TransformOrigin.Center
                val currentBoxWidthPx = size.width
                val screenWidthPx = with(density) { screenWidthDp.toPx() }
                val targetVisualCenterXOnScreenPx = (screenWidthPx / 2f) - (screenWidthPx * 1.1f)
                translationX = targetVisualCenterXOnScreenPx - (currentBoxWidthPx / 2f)
                val visualUpwardShiftPx = with(density) { (screenWidthDp * contentScaleFactor * 0.18f).toPx() }
                translationY = -visualUpwardShiftPx
            }

        .pointerInput("platter_drag") { // MULTI-TOUCH FIX: Unique key for platter drag
                android.util.Log.i("PlatterTouch", "pointerInput block initialized - platterActualRenderSizePx: $platterActualRenderSizePx")
                detectDragGestures(
            onDragStart = { touchOffset ->
                        android.util.Log.i("PlatterTouch", "onDragStart triggered! touchOffset=$touchOffset")
                        
                        // STABILIZED: Simple center calculation for consistent touch mapping
                        val centerX = platterActualRenderSizePx.width / 2f
                        val centerY = platterActualRenderSizePx.height / 2f
                        
                        val distance = sqrt((touchOffset.x - centerX).pow(2) + (touchOffset.y - centerY).pow(2))
                        val radius = platterActualRenderSizePx.width / 2f
                        
                        // Note: Removed verbose COORDINATE_FIX logging for performance
                        android.util.Log.i("PlatterTouch", "onDragStart: centerX=$centerX, centerY=$centerY, distance=$distance, radius=$radius, within=${distance <= radius}")
                        
                        if (distance <= radius) {
                            localCenterForDragCalc = Offset(centerX, centerY)
                            touchRadius = distance  // Store touch radius for potential future use
                            // Claim this gesture's pointer as the active controller
                            // Note: detectDragGestures uses the primary pointer; record it via previous event
                            // We'll set activePointerId from the next onDrag change
                            android.util.Log.i("PlatterTouch", "onDragStart: Touch within radius - Calling onPress()")
                            onPress()
                            previousAngle = atan2(
                                touchOffset.y - localCenterForDragCalc.y,
                                touchOffset.x - localCenterForDragCalc.x
                            ) * (180f / PI.toFloat())

                            android.util.Log.i("PlatterTouch", "ANGLE_CALC: raw_angle=$previousAngle° from touch=($${touchOffset.x},$${touchOffset.y}) center=($${localCenterForDragCalc.x},$${localCenterForDragCalc.y})")

                            // BACKSPIN DEBUG: Log UI finger position details
                            android.util.Log.i("BACKSPIN_DEBUG_UI", "TOUCH_START: fingerAngle=%.2f°, touchPos=(%.1f,%.1f), center=(%.1f,%.1f), distance=%.1f".format(previousAngle, touchOffset.x, touchOffset.y, localCenterForDragCalc.x, localCenterForDragCalc.y, distance))

                            // Send finger angle for angular offset calculation
                            onPressWithAngle(previousAngle)
                        } else {
                            android.util.Log.w("PlatterTouch", "onDragStart: Touch OUTSIDE radius - NOT calling onPress()")
                        }
                    },
                    onDrag = { change, _ ->
                        if (localCenterForDragCalc != Offset.Zero) {
                            // Initialize active pointer on first drag event
                            if (activePointerId == null) {
                                activePointerId = change.id
                                android.util.Log.i("PlatterTouch", "Active pointer set: $activePointerId")
                            }
                            // Ignore unrelated pointers to keep platter control isolated
                            if (activePointerId != change.id) {
                                change.consume()
                                return@detectDragGestures
                            }
                            // BOUNDARY_STABILITY: Constrain touch position to reasonable bounds around platter center
                            val platterRadius = platterActualRenderSizePx.width / 2f
                            val maxTrackingRadius = platterRadius * 2.5f // Allow tracking up to 2.5x platter radius
                            
                            val deltaX = change.position.x - localCenterForDragCalc.x
                            val deltaY = change.position.y - localCenterForDragCalc.y
                            val currentDistance = sqrt(deltaX.pow(2) + deltaY.pow(2))
                            
                            // If finger is too far from center, clamp position to max tracking radius
                            val constrainedX: Float
                            val constrainedY: Float
                            if (currentDistance > maxTrackingRadius) {
                                val scale = maxTrackingRadius / currentDistance
                                constrainedX = localCenterForDragCalc.x + (deltaX * scale)
                                constrainedY = localCenterForDragCalc.y + (deltaY * scale)
                                android.util.Log.i("BOUNDARY_STABILITY", "CONSTRAINED: distance=%.1f > max=%.1f, scale=%.3f, orig=(%.1f,%.1f) → constrained=(%.1f,%.1f)".format(currentDistance, maxTrackingRadius, scale, change.position.x, change.position.y, constrainedX, constrainedY))
                            } else {
                                constrainedX = change.position.x
                                constrainedY = change.position.y
                            }
                            
                            val currentAngle = atan2(
                                constrainedY - localCenterForDragCalc.y,
                                constrainedX - localCenterForDragCalc.x
                            ) * (180f / PI.toFloat())
                            
                            var angleDelta = currentAngle - previousAngle
                            if (angleDelta > 180) angleDelta -= 360
                            if (angleDelta < -180) angleDelta += 360

                            // BACKSPIN DEBUG: Log UI drag details
                            android.util.Log.i("BACKSPIN_DEBUG_UI", "DRAG: currentAngle=%.2f°, previousAngle=%.2f°, angleDelta=%.2f°, pos=(%.1f,%.1f)".format(currentAngle, previousAngle, angleDelta, constrainedX, constrainedY))

                            // Normalized radius factor: r / R in [0, 1+] for consistent per-mm mapping
                            val radialFactor = if (platterRadius > 0f) (currentDistance / platterRadius).coerceIn(0.0f, 2.0f) else 1f

                            // Pass angle delta and radius factor to ViewModel for final mapping
                            onDrag(angleDelta, radialFactor)
                            previousAngle = currentAngle // Keep raw angle for next calculation
                            change.consume()
                        }
                    },
                    onDragEnd = {
                        android.util.Log.i("PlatterTouch", "onDragEnd: localCenterForDragCalc=$localCenterForDragCalc")
                        if (localCenterForDragCalc != Offset.Zero) {
                            android.util.Log.i("PlatterTouch", "onDragEnd: Calling onRelease()")
                            onRelease()
                        } else {
                            android.util.Log.w("PlatterTouch", "onDragEnd: NOT calling onRelease() - localCenterForDragCalc is Zero!")
                        }
                        localCenterForDragCalc = Offset.Zero
                        activePointerId = null
                    },
                    onDragCancel = {
                        android.util.Log.i("PlatterTouch", "onDragCancel: localCenterForDragCalc=$localCenterForDragCalc")
                        if (localCenterForDragCalc != Offset.Zero) {
                            android.util.Log.i("PlatterTouch", "onDragCancel: Calling onRelease()")
                            onRelease()
                        } else {
                            android.util.Log.w("PlatterTouch", "onDragCancel: NOT calling onRelease() - localCenterForDragCalc is Zero!")
                        }
                        localCenterForDragCalc = Offset.Zero
                        activePointerId = null
                    }
                )
            }
    ) {
        Image(
            painter = painterResource(id = R.drawable.platter),
            contentDescription = "Turntable Platter Base",
            modifier = Modifier.fillMaxSize().graphicsLayer { rotationZ = visualPlatterAngle },
            contentScale = ContentScale.Fit
        )
        Image(
            painter = painterResource(id = R.drawable.vinyl),
            contentDescription = "Vinyl Record",
            modifier = Modifier.fillMaxSize().graphicsLayer { rotationZ = vinylAngle },
            contentScale = ContentScale.Fit
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomFader(
    volume: Float,
    onVolumeChange: (Float) -> Unit,
    faderAreaHeight: Dp,
    modifier: Modifier = Modifier
) {
    val knobHeight = faderAreaHeight * 0.7f
    val knobImageIntrinsicAspectRatio = 1.5f
    val knobWidth = knobHeight * knobImageIntrinsicAspectRatio

    Box(modifier = modifier) {
        Image(
            painter = painterResource(id = R.drawable.fplate),
            contentDescription = "Fader Plate",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillWidth
        )
        Slider(
            value = volume,
            onValueChange = onVolumeChange,
            valueRange = 0f..1f,
            modifier = Modifier.fillMaxSize().padding(horizontal = 85.dp), // Adjusted padding for fader knob
            thumb = {
                Image(
                    painter = painterResource(id = R.drawable.faderknob),
                    contentDescription = "Fader Knob",
                    modifier = Modifier.size(width = knobWidth, height = knobHeight),
                    contentScale = ContentScale.Fit
                )
            },
            colors = SliderDefaults.colors(
                thumbColor = Color.Transparent, // Make thumb transparent to show custom image
                activeTrackColor = Color.Transparent, // Hide default track
                inactiveTrackColor = Color.Transparent // Hide default track
            )
        )
    }
}

@Composable
fun LoadingOverlay(message: String) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier.padding(32.dp),
            colors = CardDefaults.cardColors(containerColor = Color.Black.copy(alpha = 0.8f))
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator(color = Color.White)
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = message,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}



@Composable
fun ErrorOverlay(error: AudioLoadingState.Error, onDismiss: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.7f)),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier.padding(32.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Error",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = Color.Red
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = error.userMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(onClick = onDismiss) {
                    Text("OK")
                }
            }
        }
    }
}

@Composable
fun SettingsDialog(viewModel: AppViewModel, onDismiss: () -> Unit) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier.fillMaxWidth(0.9f).padding(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color(0xFF333333))
        ) {
            Column(
                modifier = Modifier.padding(16.dp).verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text("Settings", style = MaterialTheme.typography.headlineSmall, color = Color.White)
                HorizontalDivider(
                    modifier = Modifier.padding(vertical = 8.dp),
                    thickness = 1.dp,
                    color = Color.Gray
                )

                // Account Section
                val account = viewModel.signedInAccount
                if (account == null) {
                    if (viewModel.isSigningIn) {
                        CircularProgressIndicator()
                    } else {
                        Button(onClick = { viewModel.signIn() }) {
                            Text("Sign in with Google")
                        }
                    }
                    Text("Sign in to sync purchases.", color = Color.Gray, style = MaterialTheme.typography.bodySmall)
                } else {
                    Text("Welcome, ${account.displayName}", color = Color.White)
                    Button(onClick = { viewModel.signOut() }) {
                        Text("Sign Out")
                    }
                }

                HorizontalDivider(
                    modifier = Modifier.padding(vertical = 8.dp),
                    thickness = 1.dp,
                    color = Color.Gray
                )

                // Music track volume fixed at 100% (slider removed)
                Text("Music Track Volume: 100% (Fixed)", color = Color.White, style = MaterialTheme.typography.bodyMedium)
                Spacer(modifier = Modifier.height(8.dp))

                Text("Slipmat Damping: ${(viewModel.slipmatDampingFactor * 100).roundToInt()}%", color = Color.White, style = MaterialTheme.typography.bodyMedium)
                Slider(
                    value = viewModel.slipmatDampingFactor,
                    onValueChange = viewModel::onSlipmatDampingChange,
                    valueRange = 0.0f..1.0f,  // TUNED RANGE: 0-100% for precise control curves
                    steps = 100,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    colors = SliderDefaults.colors(
                        thumbColor = MaterialTheme.colorScheme.secondary,
                        activeTrackColor = MaterialTheme.colorScheme.secondary,
                        inactiveTrackColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.24f)
                    )
                )
                Text("Lower: Slower catch-up | Higher: Faster catch-up", color = Color.Gray, style = MaterialTheme.typography.bodySmall)
                Spacer(modifier = Modifier.height(8.dp))

                Text("Slipmat Abruptness: ${String.format("%.1f", viewModel.slipmatAbruptness)}", color = Color.White, style = MaterialTheme.typography.bodyMedium)
                Slider(
                    value = viewModel.slipmatAbruptness,
                    onValueChange = viewModel::onSlipmatAbruptnessChange,
                    valueRange = 1.0f..15.0f,  // TUNED RANGE: 1-15 for precise control curves
                    steps = 28,  // Fine control
                    modifier = Modifier.fillMaxWidth(0.9f),
                    colors = SliderDefaults.colors(
                        thumbColor = MaterialTheme.colorScheme.secondary,
                        activeTrackColor = MaterialTheme.colorScheme.secondary,
                        inactiveTrackColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.24f)
                    )
                )
                Text("Lower: Gradual acceleration | Higher: Sharp snap to target", color = Color.Gray, style = MaterialTheme.typography.bodySmall)
                Spacer(modifier = Modifier.height(8.dp))

                Text("Base Friction: ${String.format("%.2f", viewModel.slipmatBaseFriction)}", color = Color.White, style = MaterialTheme.typography.bodyMedium)
                Slider(
                    value = viewModel.slipmatBaseFriction,
                    onValueChange = viewModel::onSlipmatBaseFrictionChange,
                    valueRange = 1.0f..15.0f,  // TUNED RANGE: 1-15 for precise control curves
                    steps = 28,  // Fine control
                    modifier = Modifier.fillMaxWidth(0.9f),
                    colors = SliderDefaults.colors(
                        thumbColor = MaterialTheme.colorScheme.secondary,
                        activeTrackColor = MaterialTheme.colorScheme.secondary,
                        inactiveTrackColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.24f)
                    )
                )
                Text("Lower: No slip friction | Higher: Constant friction on release", color = Color.Gray, style = MaterialTheme.typography.bodySmall)
                Spacer(modifier = Modifier.height(8.dp))

                // Touch Sensitivity
                Text("Touch Sensitivity: ${String.format("%.0f", viewModel.touchSensitivityFactor * 100)}%", color = Color.White, style = MaterialTheme.typography.bodyMedium)
                Slider(
                    value = viewModel.touchSensitivityFactor,
                    onValueChange = viewModel::onTouchSensitivityChange,
                    valueRange = 0.75f..1.25f,
                    steps = 10,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    colors = SliderDefaults.colors(
                        thumbColor = MaterialTheme.colorScheme.primary,
                        activeTrackColor = MaterialTheme.colorScheme.primary,
                        inactiveTrackColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.24f)
                    )
                )
                Text("Range narrowed: 75% slower feel to 125% faster. 100% = 1:1.", color = Color.Gray, style = MaterialTheme.typography.bodySmall)
                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Radius-aware Touch",
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                    Switch(
                        checked = viewModel.useRadiusAwareTouch,
                        onCheckedChange = { viewModel.updateUseRadiusAwareTouch(it) },
                        colors = SwitchDefaults.colors(checkedThumbColor = MaterialTheme.colorScheme.primary)
                    )
                }
                Text("1.0 at half radius; scales inward/outward to keep 1:1 around the sweet spot.", color = Color.Gray, style = MaterialTheme.typography.bodySmall)
                Spacer(modifier = Modifier.height(8.dp))

                // Max Scratch Speed
                Text("Max Scratch Speed: ${String.format("%.2fx", viewModel.maxScratchSpeed)}", color = Color.White, style = MaterialTheme.typography.bodyMedium)
                Slider(
                    value = viewModel.maxScratchSpeed,
                    onValueChange = { viewModel.updateMaxScratchSpeed(it) },
                    valueRange = 0.5f..3.0f,
                    steps = 25,
                    modifier = Modifier.fillMaxWidth(0.9f),
                    colors = SliderDefaults.colors(
                        thumbColor = MaterialTheme.colorScheme.primary,
                        activeTrackColor = MaterialTheme.colorScheme.primary,
                        inactiveTrackColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.24f)
                    )
                )
                Text("Limits maximum platter scratch playback speed (audio + visual).", color = Color.Gray, style = MaterialTheme.typography.bodySmall)
                Spacer(modifier = Modifier.height(8.dp))

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(text = "Developer Mode", color = Color.White, style = MaterialTheme.typography.bodyMedium, modifier = Modifier.weight(1f))
                    Switch(
                        checked = viewModel.isDevModeEnabled,
                        onCheckedChange = { viewModel.toggleDevMode() },
                        colors = SwitchDefaults.colors(checkedThumbColor = MaterialTheme.colorScheme.primary)
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))

                Button(
                    onClick = onDismiss,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF555555))
                ) {
                    Text("Close", color = Color.White)
                }
            }
        }
    }
}

@Composable
fun PurchaseOptionsPopup(
    onDismiss: () -> Unit, 
    onUploadAccessPurchase: () -> Unit, 
    onPremiumSubscription: () -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Choose Your Plan",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                
                // Upload + Month option
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onUploadAccessPurchase() },
                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.primaryContainer)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Upload + Premium Month",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "• Upload custom files permanently\n• 1 month full premium access\n• One-time purchase",
                            style = MaterialTheme.typography.bodySmall,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Premium Subscription option
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onPremiumSubscription() },
                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.secondaryContainer)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Premium Monthly",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "• Upload custom files\n• Full access to all tracks\n• 14-day free trial\n• Monthly subscription",
                            style = MaterialTheme.typography.bodySmall,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                TextButton(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("Maybe Later")
                }
            }
        }
    }
}

@Composable
fun SignInPrompt(viewModel: AppViewModel) {
    Dialog(onDismissRequest = { /* Non-dismissible */ }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Welcome to AYO DJ!",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Sign in with Google to sync your purchases and settings across devices.",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(24.dp))
                if (viewModel.isSigningIn) {
                    CircularProgressIndicator()
                } else {
                    Button(
                        onClick = { viewModel.signIn() },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("Sign in with Google")
                    }
                    Spacer(modifier = Modifier.height(12.dp))
                    TextButton(
                        onClick = { viewModel.showSignInPrompt = false },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = "Sign in later",
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TrackListDialog(viewModel: AppViewModel) {
    Dialog(
        onDismissRequest = { viewModel.hideTracksList() }
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.8f),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // Header
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "AyoDJ Tracks",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    IconButton(
                        onClick = { viewModel.hideTracksList() }
                    ) {
                        Icon(
                            painter = painterResource(id = android.R.drawable.ic_menu_close_clear_cancel),
                            contentDescription = "Close"
                        )
                    }
                }
                
                Divider()
                
                // Track List
                val tracks by viewModel.tracks.collectAsState()
                val subscriptionStatus by viewModel.subscriptionStatus.collectAsState()
                val downloadingTracks by viewModel.downloadingTracks.collectAsState()
                val downloadErrors by viewModel.downloadErrors.collectAsState()
                
                TrackListUI(
                    tracks = tracks,
                    subscriptionStatus = subscriptionStatus,
                    downloadingTracks = downloadingTracks,
                    downloadErrors = downloadErrors,
                    onTrackPlay = viewModel::playTrack,
                    onTrackSave = viewModel::saveTrack,
                    onUpgradeClicked = viewModel::showUpgradePrompt,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}
