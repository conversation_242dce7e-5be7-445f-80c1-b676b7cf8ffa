#include "AudioEngine.h"
#include "AudioSample.h"
#include <jni.h>
#include <android/asset_manager_jni.h>
#include <android/log.h>
#include <memory>

// Logging macros
#define APP_TAG "AyoDJ_JNI"
#define ALOGI(...) __android_log_print(ANDROID_LOG_INFO, APP_TAG, __VA_ARGS__)
#define ALOGE(...) __android_log_print(ANDROID_LOG_ERROR, APP_TAG, __VA_ARGS__)
#define ALOGW(...) __android_log_print(ANDROID_LOG_WARN, APP_TAG, __VA_ARGS__)

// Global AudioEngine instance
std::unique_ptr<AudioEngine> gAudioEngine = nullptr;

// All JNIEXPORT functions and JNI logic moved from native-lib.cpp

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_initAudioEngine(
    JNIEnv* env,
    jobject thiz,
    jobject assetManager,
    jstring cacheDirJ,
    jobject mainActivityObj)
{
    __android_log_print(ANDROID_LOG_INFO, "native-lib", "JNI: initAudioEngine called with 3 args (AssetManager, String, MainActivity)");
    if (!gAudioEngine) {
        gAudioEngine = std::make_unique<AudioEngine>();
    } else {
        gAudioEngine->release();
        gAudioEngine = std::make_unique<AudioEngine>();
    }
    const char *cacheDirNative = env->GetStringUTFChars(cacheDirJ, nullptr);
    if (cacheDirNative) {
        gAudioEngine->baseCachePath_ = cacheDirNative;
        env->ReleaseStringUTFChars(cacheDirJ, cacheDirNative);
    }
    AAssetManager* nativeAssetManager = AAssetManager_fromJava(env, assetManager);
    if (!nativeAssetManager) { gAudioEngine.reset(); return; }
    if (!gAudioEngine->init(nativeAssetManager)) {
        gAudioEngine.reset();
        return;
    }

    // Set up Java callback for track completion
    JavaVM* jvm;
    env->GetJavaVM(&jvm);
    gAudioEngine->setJavaCallback(jvm, mainActivityObj);
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_startPlayback(
        JNIEnv* env,
        jobject thiz) {
    if (gAudioEngine) {
        gAudioEngine->startStream();
    }
}



extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_setAudioNormalizationFactor(
        JNIEnv* env,
        jobject thiz,
        jfloat degreesPerFrame) {
    if (gAudioEngine) {
        gAudioEngine->setDegreesPerFrameForUnityRateInternal(static_cast<float>(degreesPerFrame));
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_playIntroAndLoopOnPlatter(
    JNIEnv* env,
    jobject thiz,
    jobject assetManager,
    jstring assetPathJ,
    jlong appVersionCode)
{
    if (!gAudioEngine) return;
    const char* assetPathC = env->GetStringUTFChars(assetPathJ, nullptr);
    if (!assetPathC) return;
    AAssetManager* nativeAssetManager = AAssetManager_fromJava(env, assetManager);
    gAudioEngine->appAssetManager_ = nativeAssetManager;
    gAudioEngine->enginePlayIntroAndLoopOnPlatter(assetPathC, (uint64_t)appVersionCode);
    env->ReleaseStringUTFChars(assetPathJ, assetPathC);
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_jniLoadAssetPlatterSample(
    JNIEnv* env,
    jobject thiz,
    jstring assetPathJ,
    jlong appVersionCode)
{
    if (!gAudioEngine) return;
    const char* assetPathC = env->GetStringUTFChars(assetPathJ, nullptr);
    if (!assetPathC) return;
    gAudioEngine->loadAssetPlatterSample(assetPathC, (uint64_t)appVersionCode);
    env->ReleaseStringUTFChars(assetPathJ, assetPathC);
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_scratchPlatterActive(
    JNIEnv* env,
    jobject thiz,
    jboolean active,
    jfloat velocity)
{
    if (gAudioEngine) {
        gAudioEngine->setScratchPlatterActive(active == JNI_TRUE, static_cast<float>(velocity));
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_setPlatterFaderVolume(
    JNIEnv* env,
    jobject thiz,
    jfloat volume)
{
    if (gAudioEngine) {
        gAudioEngine->setPlatterFaderVolumeInternal(static_cast<float>(volume));
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_releasePlatterTouch(
    JNIEnv* env,
    jobject thiz)
{
    ALOGI("JNI: releasePlatterTouch called!");
    if (gAudioEngine) {
        ALOGI("JNI: Calling gAudioEngine->releasePlatterTouchInternal()");
        gAudioEngine->releasePlatterTouchInternal();
        ALOGI("JNI: releasePlatterTouchInternal() completed");
    } else {
        ALOGE("JNI: releasePlatterTouch failed - gAudioEngine is null!");
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_releaseAudioEngine(
    JNIEnv* env,
    jobject thiz)
{
    if (gAudioEngine) {
        gAudioEngine->release();
        gAudioEngine.reset();
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_stopPlayback(
    JNIEnv* env,
    jobject thiz)
{
    if (gAudioEngine) {
        gAudioEngine->stopStream();
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_setMaxPlatterSpeed(
    JNIEnv* env,
    jobject thiz,
    jfloat newMax)
{
    if (gAudioEngine) {
        gAudioEngine->setMaxPlatterSpeed(static_cast<float>(newMax));
    }
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_high_ayodj_MainActivity_getMaxPlatterSpeed(
    JNIEnv* env,
    jobject thiz)
{
    if (gAudioEngine) {
        return gAudioEngine->getMaxPlatterSpeed();
    }
    return 0.0f;
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_jniLoadAssetMusicTrack(
    JNIEnv* env,
    jobject thiz,
    jstring assetPathJ,
    jlong appVersionCode)
{
    if (!gAudioEngine) return;
    const char* assetPathC = env->GetStringUTFChars(assetPathJ, nullptr);
    if (!assetPathC) return;
    gAudioEngine->engineLoadAssetMusic(assetPathC, (uint64_t)appVersionCode);
    env->ReleaseStringUTFChars(assetPathJ, assetPathC);
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_loadUserPlatterSample(
    JNIEnv* env,
    jobject thiz,
    jstring filePathOrUriJ,
    jint fd,
    jlong offset,
    jlong length,
    jlong modificationTimestamp)
{
    if (!gAudioEngine) return;
    const char* filePathOrUriC = env->GetStringUTFChars(filePathOrUriJ, nullptr);
    if (!filePathOrUriC) return;
    gAudioEngine->engineLoadUserPlatterSample(filePathOrUriC, fd, offset, length, (uint64_t)modificationTimestamp);
    env->ReleaseStringUTFChars(filePathOrUriJ, filePathOrUriC);
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_stopMusicTrack(
    JNIEnv* env,
    jobject thiz)
{
    if (gAudioEngine) gAudioEngine->stopMusicTrackInternal();
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_loadUserMusicTrack(
    JNIEnv* env,
    jobject thiz,
    jstring filePathOrUriJ,
    jint fd,
    jlong offset,
    jlong length,
    jlong modificationTimestamp)
{
    if (!gAudioEngine) return;
    const char* filePathOrUriC = env->GetStringUTFChars(filePathOrUriJ, nullptr);
    if (!filePathOrUriC) return;
    gAudioEngine->engineLoadUserMusicTrack(filePathOrUriC, fd, offset, length, (uint64_t)modificationTimestamp);
    env->ReleaseStringUTFChars(filePathOrUriJ, filePathOrUriC);
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_setMusicMasterVolume(
    JNIEnv* env,
    jobject thiz,
    jfloat volume)
{
    if (gAudioEngine) gAudioEngine->setMusicMasterVolumeInternal(static_cast<float>(volume));
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_high_ayodj_MainActivity_stringFromJNI(
    JNIEnv* env,
    jobject thiz)
{
    std::string hello = "Hello from C++ (modular native-lib-jni.cpp)";
    if (gAudioEngine && gAudioEngine.get() != nullptr) {
        hello += " - AudioEngine Initialized and valid.";
    } else {
        hello += " - AudioEngine IS NULL or NOT Initialized.";
    }
    return env->NewStringUTF(hello.c_str());
}

// JNI functions for VinylTracker control
extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_startVinylTracking(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        gAudioEngine->startVinylTracking();
        __android_log_print(ANDROID_LOG_INFO, "native-lib", "JNI: VinylTracker started");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, "native-lib", "JNI: Cannot start VinylTracker - engine is null");
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_stopVinylTracking(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        gAudioEngine->stopVinylTracking();
        __android_log_print(ANDROID_LOG_INFO, "native-lib", "JNI: VinylTracker stopped");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, "native-lib", "JNI: Cannot stop VinylTracker - engine is null");
    }
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_high_ayodj_MainActivity_getCurrentVinylAngle(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        return gAudioEngine->getCurrentVinylAngle();
    }
    return 0.0f;
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_high_ayodj_MainActivity_isVinylTracking(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        return gAudioEngine->isVinylTracking() ? JNI_TRUE : JNI_FALSE;
    }
    return JNI_FALSE;
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_high_ayodj_MainActivity_getUnwrappedVinylAngle(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        return gAudioEngine->getUnwrappedVinylAngle();
    }
    return 0.0f;
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_high_ayodj_MainActivity_getTotalRotations(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        return gAudioEngine->getTotalRotations();
    }
    return 0.0f;
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_resetVinylAngle(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        gAudioEngine->resetVinylAngle();
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_requestVinylSync(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        gAudioEngine->requestVinylSync();
        __android_log_print(ANDROID_LOG_INFO, "native-lib", "JNI: VinylSync requested");
    } else {
        __android_log_print(ANDROID_LOG_ERROR, "native-lib", "JNI: Cannot request VinylSync - engine is null");
    }
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_high_ayodj_MainActivity_getCurrentTickBasedVinylAngle(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        return gAudioEngine->getCurrentTickBasedVinylAngle();
    }
    return 0.0f;
}

extern "C" JNIEXPORT jlong JNICALL
Java_com_high_ayodj_MainActivity_getCurrentMasterTick(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        return static_cast<jlong>(gAudioEngine->getCurrentMasterTick());
    }
    return 0;
}

// SINGLE SOURCE OF TRUTH: JNI methods for SlipmatPhysics parameter updates
extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_setSlipmatDamping(
    JNIEnv* env,
    jobject thiz,
    jfloat damping)
{
    ALOGI("JNI: setSlipmatDamping called with value: %.4f", damping);
    if (gAudioEngine) {
        gAudioEngine->setSlipmatDamping(static_cast<float>(damping));
        ALOGI("JNI: setSlipmatDamping successfully called AudioEngine");
    } else {
        ALOGE("JNI: setSlipmatDamping failed - gAudioEngine is null!");
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_setSlipmatAbruptness(
    JNIEnv* env,
    jobject thiz,
    jfloat abruptness)
{
    ALOGI("JNI: setSlipmatAbruptness called with value: %.4f", abruptness);
    if (gAudioEngine) {
        gAudioEngine->setSlipmatAbruptness(static_cast<float>(abruptness));
        ALOGI("JNI: setSlipmatAbruptness successfully called AudioEngine");
    } else {
        ALOGE("JNI: setSlipmatAbruptness failed - gAudioEngine is null!");
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_high_ayodj_MainActivity_setSlipmatBaseFriction(
    JNIEnv* env,
    jobject thiz,
    jfloat baseFriction)
{
    ALOGI("JNI: setSlipmatBaseFriction called with value: %.4f", baseFriction);
    if (gAudioEngine) {
        gAudioEngine->setSlipmatBaseFriction(static_cast<float>(baseFriction));
        ALOGI("JNI: setSlipmatBaseFriction successfully called AudioEngine");
    } else {
        ALOGE("JNI: setSlipmatBaseFriction failed - gAudioEngine is null!");
    }
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_high_ayodj_MainActivity_getCurrentSlipmatSpeed(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        float currentSpeed = gAudioEngine->getCurrentSlipmatSpeed();
        // Note: Removed verbose JNI getCurrentSlipmatSpeed logging for performance
        return static_cast<jfloat>(currentSpeed);
    }
    ALOGE("JNI: getCurrentSlipmatSpeed failed - gAudioEngine is null!");
    return 0.0f;
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_high_ayodj_MainActivity_getTargetSlipmatSpeed(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        float targetSpeed = gAudioEngine->getTargetSlipmatSpeed();
        // Note: Removed verbose JNI getTargetSlipmatSpeed logging for performance
        return static_cast<jfloat>(targetSpeed);
    }
    ALOGE("JNI: getTargetSlipmatSpeed failed - gAudioEngine is null!");
    return 0.0f;
}

extern "C" JNIEXPORT jfloat JNICALL
Java_com_high_ayodj_MainActivity_getCurrentAudioAngleDegrees(JNIEnv *env, jobject thiz) {
    if (gAudioEngine) {
        return gAudioEngine->getCurrentAudioAngleDegrees();
    }
    return 0.0f;
}