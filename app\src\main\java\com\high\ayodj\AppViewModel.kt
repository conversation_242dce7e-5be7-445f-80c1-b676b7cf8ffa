package com.high.ayodj

import android.app.Activity
import android.app.Application
import android.content.Context
import android.net.Uri
import android.media.MediaMetadataRetriever
import android.util.Log
import org.json.JSONArray
import org.json.JSONObject
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.lifecycle.AndroidViewModel
import androidx.core.content.edit
import java.io.File
import java.util.Date
import java.util.concurrent.TimeUnit
import androidx.lifecycle.viewModelScope
import androidx.activity.result.ActivityResultLauncher
import android.content.Intent
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.tasks.Task
import com.high.ayodj.monetization.SubscriptionStatus

import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlin.math.abs // Already imported
import kotlin.math.sign // Already imported
import kotlin.math.pow
import java.util.Locale
import com.high.ayodj.monetization.*
import com.high.ayodj.monetization.MonthlyUpdateManager

data class GoogleAccount(val displayName: String)
enum class SoundType { PLATTER_SAMPLE, MUSIC_TRACK }

sealed class AudioLoadingState {
    object Idle : AudioLoadingState()
    data class Loading(val message: String, val isBackgroundOperation: Boolean = true) : AudioLoadingState()
    data class Error(val userMessage: String, val logMessage: String? = null) : AudioLoadingState()
}

class AppViewModel(
    application: Application,
    private val cloudSyncManager: CloudSyncManager,
    private val signInLauncher: ActivityResultLauncher<Intent>,
    private val onPlayIntroAndLoopOnPlatter: (filePath: String) -> Unit,
    // Removed: onNextPlatterSample: () -> Unit,
    private val onLoadUserPlatterSample: (uriString: String, modTime: Long) -> Unit, // For user content URIs
    private val onLoadAssetPlatterSample: (assetPath: String) -> Unit, // For assets by path

    // Removed: onPlayMusicTrack: () -> Unit,
    private val onStopMusicTrack: () -> Unit,
    // Removed: onNextMusicTrackAndPlay: () -> Unit,
    // Removed: onNextMusicTrackAndKeepState: () -> Unit,
    private val onLoadUserMusicTrack: (uriString: String, modTime: Long) -> Unit, // For user content URIs
    private val onLoadAssetMusicTrack: (assetPath: String) -> Unit, // For assets by path

    private val onUpdatePlatterFaderVolume: (volume: Float) -> Unit,
    private val onUpdateMusicMasterVolume: (volume: Float) -> Unit,
    private val onScratchPlatterActive: (isActive: Boolean, angleDeltaOrRate: Float) -> Unit,
    private val onReleasePlatterTouch: () -> Unit,
    private val onRequestVinylSync: () -> Unit, // Request precise sync from audio callback
    private val onGetCurrentTickBasedVinylAngle: () -> Float, // Get actual audio-based angle
    private val onGetCurrentVinylAngle: () -> Float, // NEW: Get current wrapped vinyl angle
    private val onGetUnwrappedVinylAngle: () -> Float, // NEW: Get cumulative unwrapped angle
    private val onGetTotalRotations: () -> Float, // NEW: Get total rotation count
    private val onGetCurrentAudioAngleDegrees: () -> Float, // NEW: True audio angle from frames

    private val onSetAudioNormalizationFactor: (degrees: Float) -> Unit,
    val onOpenFilePicker: (soundType: SoundType) -> Unit,

    // SINGLE SOURCE OF TRUTH: SlipmatPhysics control methods
    private val onSetSlipmatDamping: (damping: Float) -> Unit,
    private val onSetSlipmatAbruptness: (abruptness: Float) -> Unit,
    private val onSetSlipmatBaseFriction: (baseFriction: Float) -> Unit,
    private val onGetCurrentSlipmatSpeed: () -> Float,
    private val onGetTargetSlipmatSpeed: () -> Float
) : AndroidViewModel(application) {
    // Callback for pushing max scratch speed to native layer; assigned from MainActivity after construction
    var applyMaxScratchSpeedToNative: ((Float) -> Unit)? = null
    
    // Monetization components
    private val trackManager = TrackManager(application)
    private val notificationScheduler = NotificationScheduler(application)
    
    // Monetization state
    val tracks = trackManager.visibleTracks
    val subscriptionStatus = trackManager.subscriptionStatus
    val downloadingTracks = trackManager.downloadingTracks
    val downloadErrors = trackManager.downloadErrors
    var showTrackList by mutableStateOf(false)
        private set
    
    // Track notification state
    var hasNewTrackNotification by mutableStateOf(false)
        private set

    companion object {
        const val SAMPLE_LOAD_HOLD_DURATION_MS = 1500L
        const val MAX_USER_SOUNDS = 10

        private const val PREFS_NAME = "UserSoundPreferences"
        private const val KEY_PLATTER_SAMPLES = "platterSamples"
        private const val KEY_MUSIC_TRACKS = "musicTracks"
        private const val KEY_DEV_MODE = "devMode"
        private const val KEY_SEEN_TRACK_IDS = "seenTrackIds"
        private const val KEY_MUSIC_MASTER_VOLUME = "musicMasterVolume"
        private const val KEY_SLIPMAT_DAMPING = "slipmatDamping"
        private const val KEY_SLIPMAT_ABRUPTNESS = "slipmatAbruptness"
        private const val KEY_SLIPMAT_BASE_FRICTION = "slipmatBaseFriction"
    private const val KEY_TOUCH_SENSITIVITY = "touchSensitivity"
    private const val KEY_USE_RADIUS_AWARE_TOUCH = "useRadiusAwareTouch"
    private const val KEY_MAX_SCRATCH_SPEED = "maxScratchSpeed"
        private const val KEY_ACTIVE_PLAYLIST = "activePlaylist"
        private const val KEY_HARDCODED_SELECTIONS = "hardcodedSelections"

        private const val INTRO_SAMPLE_PATH = "sounds/haahhh"
    }

    var currentScreen by mutableStateOf<AppScreen>(AppScreen.Loading)
        private set
    var showSettingsDialog by mutableStateOf(false)
        private set
    var showSubscribePopup by mutableStateOf(false)
    var showTrialInfoPopup by mutableStateOf(false)
        private set
    var showPlatterSampleLoadPage by mutableStateOf(false)
    var showMusicTrackLoadPage by mutableStateOf(false)
    var showSignInPrompt by mutableStateOf(false)

    var audioLoadingState by mutableStateOf<AudioLoadingState>(AudioLoadingState.Idle)
        private set

    // Mutex to prevent simultaneous loading operations that could cause stalling
    private val loadingMutex = Mutex()

    // Cache for recently loaded tracks to prevent redundant loading operations
    private val recentlyLoadedTracks = mutableSetOf<String>()
    private val maxCacheSize = 20
      // Button states for UI
    var button1Pressed by mutableStateOf(false)
        private set
    var button2Pressed by mutableStateOf(false) 
        private set

    private var isButton1PhysicallyHeld by mutableStateOf(false)
    private var isButton2PhysicallyHeld by mutableStateOf(false)

    private var button1HoldJob: Job? = null
    private var button2HoldJob: Job? = null
    
    // Add these two new private variables to track long press state
    private var button1LongPressHandled = false
    private var button2LongPressHandled = false
    
    var visualPlatterAngle by mutableFloatStateOf(0f)
    var vinylAngle by mutableFloatStateOf(0f)
    private var isPlatterTouched by mutableStateOf(false)
    private var vinylSpeed by mutableFloatStateOf(0f)

    // STEP 2: Angular compensation to prevent jumps
    private var angularOffset = 0f
    private var compensationApplied = false
    private var lastFingerAngle = 0f  // Track last finger position for smooth dragging
    private var lastTouchTime = 0L    // Track timing for rate mapping fix
    private var uiPhaseOffset = 0f    // Visual phase offset for instant sync at touch
    
    // PRODUCTION: Rate mapping system disabled - using direct 1:1 mapping
    // TODO: Remove entire rate mapping system after confirming functionality
    private val useRateMapping = false
    private val useAggressiveMapping = false
    private val useMicroTuning = false
    private val visualPlatterRPM = 25.0f  // LOCKED: 25 RPM matches DJ turntable strobe at 60Hz
    private val degreesPerFrameAtPlatterRPM = (visualPlatterRPM / 60.0f) * 360.0f / 60.0f
    private val platterDegreesPerSecond = (visualPlatterRPM * 360f) / 60f // 150 deg/s
    private var lastUiJumpLogTimeMs = 0L
    private var lastFrameNanos: Long = 0L
    private val driveAnglesFromCompose: Boolean = true

    init {
        // PRODUCTION: Essential constants only
        Log.d("AppInit", "visualPlatterRPM = $visualPlatterRPM, degreesPerFrame = $degreesPerFrameAtPlatterRPM")

        // Set the correct audio normalization factor
        onSetAudioNormalizationFactor(degreesPerFrameAtPlatterRPM)
    }
    var slipmatDampingFactor by mutableFloatStateOf(0.32f)  // UPDATED DEFAULT: 32% damping
    var slipmatAbruptness by mutableFloatStateOf(15.0f)  // DEFAULT: 15 abruptness
    var slipmatBaseFriction by mutableFloatStateOf(15.0f)  // DEFAULT: 15 base friction
    var slipmatCurrentSpeed by mutableFloatStateOf(1.0f)
        private set
    var slipmatTargetSpeed by mutableFloatStateOf(1.0f)
        private set

    // Adjustable max scratch speed (default aligns with native default 1.5f)
    var maxScratchSpeed by mutableFloatStateOf(1.5f)
        private set
    fun updateMaxScratchSpeed(value: Float) {
        val clamped = value.coerceIn(0.1f, 5.0f)
        maxScratchSpeed = clamped
        // Persist
        saveAudioSettings() // reuse existing persistence path
        try {
            // Bridge via provided callback path in MainActivity in future; for now rely on external call site
        } catch (_: Exception) {}
    }

    // Touch → audio sensitivity factor (fine-tune mapping; 1.0 = direct, 0.5 = half)
    var touchSensitivityFactor by mutableFloatStateOf(1.0f) // DEFAULT: 1.0 = 100% sensitivity
        private set
    fun updateTouchSensitivityFactor(value: Float) {
        // Clamp to a reasonable range
        touchSensitivityFactor = value.coerceIn(0.1f, 2.0f)
    }

    fun onTouchSensitivityChange(newValue: Float) {
        updateTouchSensitivityFactor(newValue)
        saveAudioSettings()
    }

    // Optional: scale drag by radial distance with 1.0 at half-radius
    var useRadiusAwareTouch by mutableStateOf(false)  // DEFAULT: OFF
        private set
    fun updateUseRadiusAwareTouch(enabled: Boolean) {
        useRadiusAwareTouch = enabled
        saveAudioSettings()
    }

    // TODO: Remove this entire block after confirming abandoned tick-based system is not needed
    // Tick-based synchronization control - abandoned/disabled
    // private var useTickBasedSync by mutableStateOf(true)
    // private fun getTickBasedVinylAngle(): Float = vinylAngle
    // fun toggleTickBasedSync() { useTickBasedSync = !useTickBasedSync }

    // CUMULATIVE ROTATION TRACKING: Debug and test functionality
    private fun startRotationTracking() {
        viewModelScope.launch {
            var logCounter = 0
            while (true) {
                try {
                    if (logCounter++ % 300 == 0) { // Every ~5 seconds
                        val wrapped = onGetCurrentVinylAngle()
                        val unwrapped = onGetUnwrappedVinylAngle()
                        val rotations = onGetTotalRotations()
                        Log.i("CUMULATIVE_TRACKING", "UI_Wrapped=%.1f°, Audio_Unwrapped=%.1f°, Total_Rotations=%.2f".format(
                            vinylAngle, unwrapped, rotations
                        ))
                    }
                } catch (e: Exception) {
                    // Ignore errors for debug tracking
                }
                delay(100) // Check every 100ms
            }
        }
    }


    val userPlatterSamplesList: SnapshotStateList<UserSoundItem> = mutableStateListOf()
    val userMusicTracksList: SnapshotStateList<UserSoundItem> = mutableStateListOf()

    var platterFaderVolume by mutableFloatStateOf(0.0f)
        private set
    var isMusicPlaying by mutableStateOf(false)
        private set

    // These lists will store the combined asset paths and user URI strings
    // They are populated from userPlatterSamplesList and userMusicTracksList
    // after loading from prefs and adding defaults.
    private var platterSamplePaths: List<String> = emptyList()
    private var currentPlatterSampleIndex by mutableIntStateOf(0)

    private var musicTrackPaths: List<String> = emptyList()
    private var currentMusicTrackIndex by mutableIntStateOf(0)

    var musicMasterVolume by mutableFloatStateOf(1.0f) // FIXED: 100% volume (no longer user-adjustable)
        private set

    var signedInAccount: GoogleAccount? by mutableStateOf(null)
        private set

    var isSigningIn: Boolean by mutableStateOf(false)
        private set

    // Dev mode state with persistence
    var isDevModeEnabled by mutableStateOf(false)
        private set

    // Notification system with rotating emojis
    private val notificationEmojis = listOf("🔥", "🎵", "🎶", "💿", "🎧", "🎤", "🎸", "🥁")
    private var currentEmojiIndex by mutableIntStateOf(0)
    var currentNotificationEmoji by mutableStateOf("🔥")
        private set
    
    // Track which tracks the user has already seen to avoid showing notification on every app load
    private val seenTrackIds = mutableSetOf<String>()
    
    init {
        val signedInAccount = cloudSyncManager.getSignedInAccount()
        if (signedInAccount == null) {
            showSignInPrompt = true
        } else {
            this.signedInAccount = GoogleAccount(displayName = signedInAccount.displayName ?: "User")
        }
        
        // Load dev mode state from preferences
        loadDevModeState()

        // Load audio settings from preferences
        loadAudioSettings()

        // Load playlist from persistence
        loadPlaylistFromPersistence()

        // Load notification state from persistence
        loadNotificationState()

        // Check for new track notifications
        checkForNewTrackNotifications()
        
        // Monitor track changes and update notification status + music library
        viewModelScope.launch {
            tracks.collect { currentTracks ->
                checkForNewTrackNotifications()
                updateMusicLibraryWithWeeklyTracks()
                // PRODUCTION SAFETY: Clean any unreleased tracks that may have slipped through
                validateAndCleanPlaylists()
            }
        }
        
        // REVERTED: Back to original working system
        onSetAudioNormalizationFactor(degreesPerFrameAtPlatterRPM)

        // Load user-saved sounds from SharedPreferences
        val loadedPlatterSamples = loadUserSoundList(KEY_PLATTER_SAMPLES)
        userPlatterSamplesList.addAll(loadedPlatterSamples)

        val loadedMusicTracks = loadUserSoundList(KEY_MUSIC_TRACKS)
        userMusicTracksList.addAll(loadedMusicTracks)

        // Clean URI lists to remove any duplicates
        cleanURILists()

        // Re-detect sources for existing tracks that might not have proper source identification
        // Must happen AFTER tracks are loaded
        redetectSourcesForExistingTracks()

        // Add Ayo Feats signature sounds if not already present from saved user list
        val ayoFeatsPlatterPaths = listOf("sounds/sample1", "sounds/sample2")
        ayoFeatsPlatterPaths.forEach { path ->
            if (!userPlatterSamplesList.any { it.filePath == path }) { // Check if path already exists
                val ayoFeatsName = when (path) {
                    "sounds/sample1" -> "Vinyl Scratch Classic - Ayo Feats!"
                    "sounds/sample2" -> "Turntable Thunder - Ayo Feats!"
                    else -> path.substringAfterLast('/') + " - Ayo Feats!"
                }
                userPlatterSamplesList.add(UserSoundItem(
                    filePath = path,
                    displayName = ayoFeatsName,
                    saveToAutoLoad = getHardcodedSelectionState(path, SoundType.PLATTER_SAMPLE),
                    isHardcoded = true,
                    source = TrackSource.AYO_OFFICIAL // Hardcoded items are Ayo official content
                ))
            }
        }
        userPlatterSamplesList.sortWith(compareBy<UserSoundItem> { it.isHardcoded }.thenBy { it.displayName })
        this.platterSamplePaths = userPlatterSamplesList.filter { it.saveToAutoLoad }.map { it.filePath }
        Log.d("AppViewModel_Init", "Platter paths initialized: ${platterSamplePaths.size} total, paths: ${platterSamplePaths.take(3)}...")


        val defaultMusicAssetPaths = listOf<String>() // Weekly tracks auto-populate via monitoring
        defaultMusicAssetPaths.forEach { path ->
            if (!userMusicTracksList.any { it.filePath == path }) {
                userMusicTracksList.add(UserSoundItem(
                    filePath = path,
                    displayName = path.substringAfterLast('/') + " (Default)",
                    saveToAutoLoad = true,
                    isHardcoded = true,
                    source = TrackSource.AYO_OFFICIAL // Hardcoded items are Ayo official content
                ))            }
        }
        
        // Add Ayo Feats signature music tracks if not already present from saved user list
        val ayoFeatsMusicPaths = listOf("sounds/music1", "sounds/music2")
        ayoFeatsMusicPaths.forEach { path ->
            if (!userMusicTracksList.any { it.filePath == path }) { // Check if path already exists
                val ayoFeatsName = when (path) {
                    "sounds/music1" -> "Vinyl Groove Classic - Ayo Feats!"
                    "sounds/music2" -> "Turntable Beats - Ayo Feats!"
                    else -> path.substringAfterLast('/') + " - Ayo Feats!"
                }
                userMusicTracksList.add(UserSoundItem(
                    filePath = path,
                    displayName = ayoFeatsName,
                    saveToAutoLoad = getHardcodedSelectionState(path, SoundType.MUSIC_TRACK),
                    isHardcoded = true,
                    source = TrackSource.AYO_OFFICIAL // Hardcoded items are Ayo official content
                ))
            }
        }
        
        // Initial weekly tracks will be added via track monitoring system
        userMusicTracksList.sortWith(compareBy<UserSoundItem> { it.isHardcoded }.thenBy { it.displayName })
        this.musicTrackPaths = userMusicTracksList.filter { it.saveToAutoLoad }.map { it.filePath }
        Log.d("AppViewModel_Init", "Music paths initialized: ${musicTrackPaths.size} total, paths: ${musicTrackPaths.take(3)}...")

        audioLoadingState = AudioLoadingState.Loading("Initializing audio...", isBackgroundOperation = true) // Set on Main thread before launching IO
        viewModelScope.launch(Dispatchers.IO) { // Background loading for initial sample
            try {
                // INTRO PLAYBACK GUARD: Ensure we only trigger intro once per process
                if (!hasIntroPlayed) {
                    hasIntroPlayed = true
                    onPlayIntroAndLoopOnPlatter(INTRO_SAMPLE_PATH)
                } else {
                    Log.i("AppViewModel_Init", "Intro already played this session; skipping intro trigger")
                }

                // SAFE WINDOW: Give the intro sound time to play before allowing main controls
                // This prevents the main platter control logic from interfering with the intro
                delay(2500) // 2.5 seconds safe window for intro playback

                // Now load the first *user-selectable* sample in the background.
                val firstUserSample = userPlatterSamplesList.getOrNull(0)
                if (firstUserSample != null) {
                    if (firstUserSample.filePath.startsWith("content://")) {
                        onLoadUserPlatterSample(firstUserSample.filePath, firstUserSample.modificationTimestamp)
                    } else {
                        onLoadAssetPlatterSample(firstUserSample.filePath)
                    }
                } else {
                    Log.w("AppViewModel_Init", "No initial user-selectable sample to load.")
                }
                
            } catch (e: Exception) {
                val errorMessage = "Failed to initialize audio: ${e.message}"
                Log.e("AppViewModel_Init", errorMessage, e)
                withContext(Dispatchers.Main) {
                    audioLoadingState = AudioLoadingState.Error("Initialization failed.", errorMessage)
                }
                // Continue anyway - visual animation should work regardless
            } finally {
                // Ensure Idle state is set if not error, or if error was already set.
                // If an error occurred, Error state is already set. Otherwise, set to Idle.
                if (audioLoadingState !is AudioLoadingState.Error) {
                    withContext(Dispatchers.Main) {
                        audioLoadingState = AudioLoadingState.Idle
                    }
                }
            }
        }
        
        // Initialize monetization system
        initializeMonetizationSystem()

        // Start cumulative rotation tracking for debugging
        startRotationTracking()

        // Slipmat telemetry updater for visualization/debug
        viewModelScope.launch {
            while (true) {
                try {
                    slipmatCurrentSpeed = onGetCurrentSlipmatSpeed()
                    slipmatTargetSpeed = onGetTargetSlipmatSpeed()
                } catch (_: Exception) {
                    // ignore
                }
                delay(50)
            }
        }

        // Loading operations will be synchronized using mutex

        if (!driveAnglesFromCompose) {
            // Fallback 60fps loop (disabled by default)
            viewModelScope.launch {
                while (true) {
                    refreshAngles(frameTimeNanos = System.nanoTime())
                    delay(16)
                }
            }
        }

        // HIGH-FREQUENCY VISUAL UPDATE: Bypass vsync during touch for perfect responsiveness
        viewModelScope.launch {
            while (true) {
                if (isPlatterTouched) {
                    // During touch: Update at ~240fps (4.17ms intervals) for ultra-responsive visuals
                    refreshAngles(frameTimeNanos = System.nanoTime())
                    delay(4) // 240fps = ~4.17ms per frame
                } else {
                    // Not touching: Sleep longer to save CPU, let vsync handle updates
                    delay(50) // Check touch state every 50ms when idle
                }
            }
        }

        // Handle UI screen transition
        viewModelScope.launch {
            delay(1000) // Wait for splash screen animation
            currentScreen = AppScreen.Main
        }
    }

    // Session guard for intro playback
    private var hasIntroPlayed = false

    // Called from Compose per frame for near-zero-lag visual updates
    fun refreshAngles(frameTimeNanos: Long) {
        val dtSeconds = if (lastFrameNanos == 0L) 1f / 60f else (frameTimeNanos - lastFrameNanos).coerceAtLeast(0L) / 1_000_000_000f
        lastFrameNanos = frameTimeNanos

        // Platter visual spin at true dt
        visualPlatterAngle = (visualPlatterAngle + platterDegreesPerSecond * dtSeconds) % 360f

        // Vinyl visual angle mirrors audio physics directly
        try {
            val audioAngle = onGetCurrentAudioAngleDegrees()
            var vinyl = (audioAngle + uiPhaseOffset) % 360f
            if (vinyl < 0f) vinyl += 360f

            // Optional jump detection
            val prev = vinylAngle
            var diff = (vinyl - prev)
            while (diff > 180f) diff -= 360f
            while (diff < -180f) diff += 360f
            if (kotlin.math.abs(diff) >= 60f) {
                val now = System.currentTimeMillis()
                if (now - lastUiJumpLogTimeMs > 1000L) {
                    android.util.Log.w("UI_SYNC_JUMP", "Detected UI angle jump: prev=%.2f°, new=%.2f°, diff=%.2f°".format(prev, vinyl, diff))
                    lastUiJumpLogTimeMs = now
                }
            }

            vinylAngle = vinyl
        } catch (_: Exception) {
            // Keep previous angle on transient JNI failure
        }
    }

    /**
     * Execute a loading operation with mutex synchronization to prevent simultaneous loading that could cause stalling
     */
    private suspend fun executeLoadingOperation(operation: suspend () -> Unit) {
        loadingMutex.withLock {
            operation()
        }
    }

    /**
     * Check if a track was recently loaded to avoid redundant loading operations
     */
    private fun isRecentlyLoaded(trackPath: String): Boolean {
        return recentlyLoadedTracks.contains(trackPath)
    }

    /**
     * Mark a track as recently loaded and manage cache size
     */
    private fun markAsRecentlyLoaded(trackPath: String) {
        recentlyLoadedTracks.add(trackPath)
        // Keep cache size manageable
        if (recentlyLoadedTracks.size > maxCacheSize) {
            val iterator = recentlyLoadedTracks.iterator()
            repeat(recentlyLoadedTracks.size - maxCacheSize) {
                if (iterator.hasNext()) {
                    iterator.next()
                    iterator.remove()
                }
            }
        }
    }

    fun handleButton1Press() {
        if (button1LongPressHandled) return
        executeButton1Action()
    }

    private fun executeButton1Action() {
        Log.d("AppViewModel_Button1", "executeButton1Action called - Current paths size: ${platterSamplePaths.size}")
        
        if (platterSamplePaths.isEmpty()) {
            Log.w("AppViewModel_Button1", "No platter samples available.")
            return
        }
        if (audioLoadingState is AudioLoadingState.Loading) return

        val oldIndex = currentPlatterSampleIndex
        
        // Ensure current index is valid before incrementing
        if (currentPlatterSampleIndex >= platterSamplePaths.size || currentPlatterSampleIndex < 0) {
            Log.w("AppViewModel_Button1", "Index out of bounds, resetting: $currentPlatterSampleIndex -> 0")
            currentPlatterSampleIndex = 0
        } else {
            currentPlatterSampleIndex = (currentPlatterSampleIndex + 1) % platterSamplePaths.size
        }
        
        val nextPath = platterSamplePaths[currentPlatterSampleIndex]
        val displayName = nextPath.substringAfterLast('/')
        
        Log.d("AppViewModel_Button1", "Platter cycling: $oldIndex -> $currentPlatterSampleIndex (of ${platterSamplePaths.size}) - Loading: $displayName")
        Log.d("AppViewModel_Button1", "Current path list: ${platterSamplePaths.joinToString { it.substringAfterLast('/') }}")

        audioLoadingState = AudioLoadingState.Loading("Loading $displayName...", isBackgroundOperation = true)

        viewModelScope.launch(Dispatchers.IO) {
            executeLoadingOperation {
                try {
                    // Check cache first to avoid redundant loading
                    if (isRecentlyLoaded(nextPath)) {
                        Log.d("AppViewModel_Button1", "Track $nextPath recently loaded, but forcing reload for cycling")
                        // For cycling, we still want audio to change even if recently loaded
                        // Clear this item from cache to allow reload
                        recentlyLoadedTracks.remove(nextPath)
                    }

                    // Use centralized loading logic
                    val userSoundItem = userPlatterSamplesList.find { it.filePath == nextPath }
                    loadFileConsistently(nextPath, SoundType.PLATTER_SAMPLE, userSoundItem)

                    // Mark as recently loaded after successful load
                    markAsRecentlyLoaded(nextPath)

                    withContext(Dispatchers.Main) {
                        vinylAngle = 0f
                        vinylSpeed = 0f
                        // Log.d("AppViewModel_Button1", "New platter sample index (UI): $currentPlatterSampleIndex. Vinyl angle/speed reset.")
                    }
                } catch (e: Exception) {
                    val errorMsg = "Error loading platter sample ${nextPath.substringAfterLast('/')}"
                    Log.e("AppViewModel_Button1", "$errorMsg: ${e.message}", e)
                    withContext(Dispatchers.Main) {
                        audioLoadingState = AudioLoadingState.Error(errorMsg, e.message)
                    }
                } finally {
                    if (audioLoadingState !is AudioLoadingState.Error) {
                        withContext(Dispatchers.Main) {
                            audioLoadingState = AudioLoadingState.Idle
                        }
                    }
                }
            }
        }
    }

    fun handleButton1Hold() {
        Log.i("AppViewModel_Button1", "handleButton1Hold() CALLED")
        // Initialize the load page with proper filtering before showing it
        initializeLoadPageState(SoundType.PLATTER_SAMPLE)
        showPlatterSampleLoadPage = true
    }

    fun handleButton2Press() {
        if (button2LongPressHandled) {
            Log.d("AppViewModel_Button2", "Tap ignored due to prior long press.")
            return
        }
        Log.i("AppViewModel_Button2", "handleButton2Press() CALLED. isMusicPlaying: $isMusicPlaying")
        executeButton2Action()
    }

    private fun executeButton2Action() {
        Log.i("AppViewModel_Button2", "executeButton2Action() CALLED. isMusicPlaying: $isMusicPlaying")
        Log.d("AppViewModel_Button2", "Current music paths size: ${musicTrackPaths.size}")
        
        if (musicTrackPaths.isEmpty()) {
            Log.w("AppViewModel_Button2", "No music tracks available.")
            return
        }
        if (audioLoadingState is AudioLoadingState.Loading) {
            // Log.d("AppViewModel_Button2", "Already processing audio, ignoring press.")
            return
        }

        val nextPath: String
        val loadingMessage: String

        if (isMusicPlaying) {
            val oldIndex = currentMusicTrackIndex
            
            // Ensure current index is valid before incrementing
            if (currentMusicTrackIndex >= musicTrackPaths.size || currentMusicTrackIndex < 0) {
                Log.w("AppViewModel_Button2", "Music index out of bounds, resetting: $currentMusicTrackIndex -> 0")
                currentMusicTrackIndex = 0
            }
            
            val nextIndex = (currentMusicTrackIndex + 1) % musicTrackPaths.size
            nextPath = musicTrackPaths[nextIndex]
            loadingMessage = "Loading next track: ${nextPath.substringAfterLast('/')}..."
            Log.d("AppViewModel_Button2", "Music cycling: $oldIndex -> $nextIndex (of ${musicTrackPaths.size}) - Loading: ${nextPath.substringAfterLast('/')}")
            Log.d("AppViewModel_Button2", "Current music path list: ${musicTrackPaths.joinToString { it.substringAfterLast('/') }}")
        } else {
            val oldIndex = currentMusicTrackIndex
            
            // Ensure current index is valid before selecting
            val trackIndex = if (currentMusicTrackIndex < 0 || currentMusicTrackIndex >= musicTrackPaths.size) {
                Log.w("AppViewModel_Button2", "Music index out of bounds, resetting: $currentMusicTrackIndex -> 0")
                0
            } else {
                currentMusicTrackIndex
            }
            
            if (musicTrackPaths.isNotEmpty()) {
                nextPath = musicTrackPaths[trackIndex]
                loadingMessage = "Loading track: ${nextPath.substringAfterLast('/')}..."
                Log.d("AppViewModel_Button2", "Music starting: index $oldIndex -> $trackIndex (of ${musicTrackPaths.size}) - Loading: ${nextPath.substringAfterLast('/')}")
                Log.d("AppViewModel_Button2", "Current music path list: ${musicTrackPaths.joinToString { it.substringAfterLast('/') }}")
            } else {
                Log.w("AppViewModel_Button2", "No music tracks to play.")
                return
            }
        }
        audioLoadingState = AudioLoadingState.Loading(loadingMessage)
        
        viewModelScope.launch(Dispatchers.IO) {
            var playingStateShouldChangeOnMain = false
            var newTrackIndexOnMain: Int  // Will be updated based on logic below
            try {
                if (isMusicPlaying) {
                    // Log.d("AppViewModel_Button2", "Button 2 Press (Music Playing): Next Music Track & Keep Playing")
                    newTrackIndexOnMain = (currentMusicTrackIndex + 1) % musicTrackPaths.size
                    val pathToLoad = musicTrackPaths[newTrackIndexOnMain] // Use the already determined nextPath for clarity if preferred
                    // Log.d("AppViewModel_Button2", "Next music track: '$pathToLoad' at index $newTrackIndexOnMain")
                    
                    // Use centralized loading logic
                    val userSoundItem = userMusicTracksList.find { it.filePath == pathToLoad }
                    loadFileConsistently(pathToLoad, SoundType.MUSIC_TRACK, userSoundItem)
                } else {
                    // Log.d("AppViewModel_Button2", "Button 2 Press (Music Stopped): Play Current or First Music Track")
                     newTrackIndexOnMain = if (currentMusicTrackIndex < 0 || currentMusicTrackIndex >= musicTrackPaths.size) 0 else currentMusicTrackIndex

                    if (musicTrackPaths.isNotEmpty()) {
                        val trackToPlay = musicTrackPaths[newTrackIndexOnMain]
                        // Log.d("AppViewModel_Button2", "Playing music track: '$trackToPlay' at index $newTrackIndexOnMain")
                        // Use centralized loading logic
                        val userSoundItem = userMusicTracksList.find { it.filePath == trackToPlay }
                        loadFileConsistently(trackToPlay, SoundType.MUSIC_TRACK, userSoundItem)
                        playingStateShouldChangeOnMain = true
                    } else {
                        // This case should ideally be caught by the initial check, but as a safeguard:
                        Log.w("AppViewModel_Button2", "No music tracks to play (logic error or race condition).")
                        // Reset loading state if we somehow got here and aren't loading.
                        withContext(Dispatchers.Main) { audioLoadingState = AudioLoadingState.Idle }
                        return@launch
                    }
                }
                
                withContext(Dispatchers.Main) {
                    currentMusicTrackIndex = newTrackIndexOnMain
                    if (playingStateShouldChangeOnMain) {
                        isMusicPlaying = true
                        dismissNewTrackNotification("music started playing")
                    }
                }
            } catch (e: Exception) {
                val errorMsg = "Error preparing music track"
                Log.e("AppViewModel_Button2", "$errorMsg: ${e.message}", e)
                withContext(Dispatchers.Main) {
                    audioLoadingState = AudioLoadingState.Error(errorMsg, e.message)
                }
            } finally {
                if (audioLoadingState !is AudioLoadingState.Error) {
                    withContext(Dispatchers.Main) {
                        audioLoadingState = AudioLoadingState.Idle
                    }
                }
                // Log.d("AppViewModel_Button2", "Music playing: $isMusicPlaying, Current track index (UI): $currentMusicTrackIndex")
            }
        }
    }

    fun handleButton2DoublePress() {
        Log.i("AppViewModel_Button2", "handleButton2DoublePress() CALLED. isMusicPlaying: $isMusicPlaying")

        if (isMusicPlaying) {
            // Log.d("AppViewModel_Button2", "Button 2 Double Press (Music Playing): Stop Music Track")
            onStopMusicTrack() // This is likely quick
            isMusicPlaying = false
            // Log.d("AppViewModel_Button2", "Music playing set to: $isMusicPlaying, Current track index (UI): $currentMusicTrackIndex")
        } else {
            if (musicTrackPaths.isEmpty()) {
                Log.w("AppViewModel_Button2_Double", "No music tracks available to play on double press.")
                return
            }
            if (audioLoadingState is AudioLoadingState.Loading) {
                // Log.d("AppViewModel_Button2_Double", "Already processing audio, ignoring double press to play."); 
                return
            }

            val nextIndex = (currentMusicTrackIndex + 1) % musicTrackPaths.size
            val nextPath = musicTrackPaths[nextIndex]
            val displayName = nextPath.substringAfterLast('/')

            // Log.d("AppViewModel_Button2", "Button 2 Double Press (Music Stopped): Next Music Track & Play")
            audioLoadingState = AudioLoadingState.Loading("Loading $displayName...", isBackgroundOperation = true)
            viewModelScope.launch(Dispatchers.IO) {
                executeLoadingOperation {
                    var newTrackIndexOnMain: Int  // Will be updated
                    try {
                        newTrackIndexOnMain = (currentMusicTrackIndex + 1) % musicTrackPaths.size // Recalculate for safety within coroutine context
                        val pathToLoad = musicTrackPaths[newTrackIndexOnMain]

                        // CRITICAL: Validate track is not from an unreleased weekly asset
                        if (pathToLoad.startsWith("weekly/")) {
                            // Check if this weekly track is actually released
                            val weeklyTrack = tracks.value.find { track ->
                                "weekly/${track.filename}" == pathToLoad
                            }
                            if (weeklyTrack?.isYetToBeReleased() == true) {
                                Log.w("AppViewModel", "Skipping unreleased track in button cycle: $pathToLoad")
                                // Skip to next track or stop playing
                                withContext(Dispatchers.Main) {
                                    isMusicPlaying = false
                                    audioLoadingState = AudioLoadingState.Idle
                                }
                                return@executeLoadingOperation
                            }
                        }

                        // Log.d("AppViewModel_Button2", "Next music track (and play): '$pathToLoad' at index $newTrackIndexOnMain"

                        // Check cache first to avoid redundant loading
                        if (isRecentlyLoaded(pathToLoad)) {
                            Log.d("AppViewModel_Button2", "Track $pathToLoad recently loaded, using cached version")
                            withContext(Dispatchers.Main) {
                                currentMusicTrackIndex = newTrackIndexOnMain
                                isMusicPlaying = true
                                audioLoadingState = AudioLoadingState.Idle
                                dismissNewTrackNotification("music started playing")
                            }
                            return@executeLoadingOperation
                        }

                        if (pathToLoad.startsWith("content://")) {
                            val userSoundItem = userMusicTracksList.find { it.filePath == pathToLoad }
                            if(userSoundItem != null) {
                                onLoadUserMusicTrack(pathToLoad, userSoundItem.modificationTimestamp)
                            }
                        } else {
                            onLoadAssetMusicTrack(pathToLoad)
                        }

                        // Mark as recently loaded after successful load
                        markAsRecentlyLoaded(pathToLoad)
                        withContext(Dispatchers.Main) {
                            currentMusicTrackIndex = newTrackIndexOnMain
                            isMusicPlaying = true
                            dismissNewTrackNotification("music started playing")
                        }
                    } catch (e: Exception) {
                        val errorMsg = "Error loading track ${nextPath.substringAfterLast('/')}"
                        Log.e("AppViewModel_Button2_Double", "$errorMsg: ${e.message}", e)
                        withContext(Dispatchers.Main) {
                            audioLoadingState = AudioLoadingState.Error(errorMsg, e.message)
                        }
                    } finally {
                        if (audioLoadingState !is AudioLoadingState.Error) {
                            withContext(Dispatchers.Main) {
                                audioLoadingState = AudioLoadingState.Idle
                            }
                        }
                        // Log.d("AppViewModel_Button2", "Music playing: $isMusicPlaying, Current track index (UI): $currentMusicTrackIndex after double press.")
                    }
                }
            }
        }
    }
    fun handleButton2Hold() {
        Log.i("AppViewModel_Button2", "handleButton2Hold() CALLED")
        // Initialize the load page with proper filtering before showing it
        initializeLoadPageState(SoundType.MUSIC_TRACK)
        showMusicTrackLoadPage = true
    }

    /**
     * Called when a music track completes naturally (from native callback)
     * Automatically loads the next track in the playlist with looping
     */
    fun onMusicTrackCompleted() {
        Log.i("AppViewModel_AutoNext", "Music track completed - loading next track automatically")

        if (musicTrackPaths.isEmpty()) {
            Log.w("AppViewModel_AutoNext", "No music tracks available for auto-progression")
            isMusicPlaying = false
            return
        }

        if (audioLoadingState is AudioLoadingState.Loading) {
            Log.d("AppViewModel_AutoNext", "Already loading audio, skipping auto-progression")
            return
        }

        // Calculate next track index with looping
        val nextIndex = (currentMusicTrackIndex + 1) % musicTrackPaths.size
        val nextPath = musicTrackPaths[nextIndex]
        val displayName = nextPath.substringAfterLast('/')

        Log.i("AppViewModel_AutoNext", "Auto-loading next track: $displayName (index $nextIndex)")
        audioLoadingState = AudioLoadingState.Loading("Auto-loading: $displayName...", isBackgroundOperation = true)

        viewModelScope.launch(Dispatchers.IO) {
            executeLoadingOperation {
                try {
                    // Validate track is not from an unreleased weekly asset
                    if (nextPath.startsWith("weekly/")) {
                        val weeklyTrack = tracks.value.find { track ->
                            "weekly/${track.filename}" == nextPath
                        }
                        if (weeklyTrack?.isYetToBeReleased() == true) {
                            Log.w("AppViewModel_AutoNext", "Skipping unreleased track in auto-progression: $nextPath")
                            withContext(Dispatchers.Main) {
                                isMusicPlaying = false
                                audioLoadingState = AudioLoadingState.Idle
                            }
                            return@executeLoadingOperation
                        }
                    }

                    // Load the next track using centralized logic
                    val userSoundItem = userMusicTracksList.find { it.filePath == nextPath }
                    loadFileConsistently(nextPath, SoundType.MUSIC_TRACK, userSoundItem)

                    // Mark as recently loaded and update state
                    markAsRecentlyLoaded(nextPath)
                    withContext(Dispatchers.Main) {
                        currentMusicTrackIndex = nextIndex
                        isMusicPlaying = true
                        dismissNewTrackNotification("auto-progression started")
                        Log.i("AppViewModel_AutoNext", "Successfully auto-loaded track: $displayName")
                    }
                } catch (e: Exception) {
                    val errorMsg = "Error auto-loading track $displayName"
                    Log.e("AppViewModel_AutoNext", "$errorMsg: ${e.message}", e)
                    withContext(Dispatchers.Main) {
                        audioLoadingState = AudioLoadingState.Error(errorMsg, e.message)
                        isMusicPlaying = false
                    }
                }
            }
        }
    }

    // Add function to open settings from load screens
    fun openSettingsFromLoadScreen() {
        Log.i("AppViewModel_Settings", "Opening settings from load screen")
        showSettingsDialog = true
    }

    // Add function to handle file loading attempts with proper subscription check
    fun attemptFileLoad(type: SoundType) {
        Log.i("AppViewModel_FileLoad", "attemptFileLoad() called for $type")
        
        val currentStatus = trackManager.getSubscriptionStatus()
        if (currentStatus.canImportCustomFiles()) {
            Log.d("AppViewModel_FileLoad", "User has file import permissions ($currentStatus): Opening file picker for $type")
            onOpenFilePicker(type)
        } else {
            Log.d("AppViewModel_FileLoad", "User lacks file import permissions ($currentStatus): Showing subscribe popup for file loading")
            showSubscribePopup = true
        }
    }

    fun onPlatterFaderVolumeChange(newVolume: Float) {
        platterFaderVolume = newVolume.coerceIn(0f, 1f)
        // Log.d("AppViewModel_Fader", "Platter Fader Volume changed to: $platterFaderVolume")
        onUpdatePlatterFaderVolume(platterFaderVolume)
    }

    fun onMusicMasterVolumeChange(newVolume: Float) {
        musicMasterVolume = newVolume.coerceIn(0f, 1f)
        // Log.d("AppViewModel_Settings", "Music Master Volume changed to: $musicMasterVolume")
        onUpdateMusicMasterVolume(musicMasterVolume)
        saveAudioSettings()
    }

    fun onSlipmatDampingChange(newDamping: Float) {
        slipmatDampingFactor = newDamping.coerceIn(0.0f, 1.0f)
        onSetSlipmatDamping(slipmatDampingFactor)
        saveAudioSettings()
    }

    fun onSlipmatAbruptnessChange(newAbruptness: Float) {
        slipmatAbruptness = newAbruptness.coerceIn(1.0f, 15.0f)
        onSetSlipmatAbruptness(slipmatAbruptness)
        saveAudioSettings()
    }

    fun onSlipmatBaseFrictionChange(newBaseFriction: Float) {
        slipmatBaseFriction = newBaseFriction.coerceIn(1.0f, 15.0f)
        onSetSlipmatBaseFriction(slipmatBaseFriction)
        saveAudioSettings()
    }



    fun closeSettingsDialog() {
        showSettingsDialog = false
    }

    fun signIn() {
        isSigningIn = true
        cloudSyncManager.signIn(signInLauncher)
    }

    fun signOut() {
        cloudSyncManager.signOut {
            signedInAccount = null
        }
    }

    fun handleGoogleSignInResult(task: Task<GoogleSignInAccount>) {
        cloudSyncManager.handleSignInResult(task,
            onSuccess = { account ->
                signedInAccount = GoogleAccount(displayName = account.displayName ?: "User")
                isSigningIn = false
                showSignInPrompt = false
            },
            onError = {
                isSigningIn = false
                // Optionally, show an error message to the user
            }
        )
    }

    fun onPlatterTouchDown() {
        isPlatterTouched = true
        vinylSpeed = 0f
        compensationApplied = false  // Reset for new touch sequence
        Log.i("HIGH_FREQ_VISUAL", "Touch DOWN - Enabling high-frequency visual updates")
        // STEP 3: Don't call onScratchPlatterActive yet - wait for angular compensation
    }

    fun onPlatterTouchDownWithAngle(fingerAngle: Float) {
        // BACKSPIN DEBUG: Log initial positions
        val currentUIAngle = vinylAngle  // Current UI position from state
    val currentTickAngle = onGetCurrentTickBasedVinylAngle()  // Get actual audio-based angle
    val currentAudioAngle = try { onGetCurrentAudioAngleDegrees() } catch (_: Exception) { currentTickAngle }
    Log.i("BACKSPIN_DEBUG", "TOUCH_DOWN: fingerAngle=%.2f°, UI_vinylAngle=%.2f°, AUDIO_tickAngle=%.2f°, AUDIO_frameAngle=%.2f°".format(fingerAngle, currentUIAngle, currentTickAngle, currentAudioAngle))
        
        // CRITICAL FIX: Calculate offset using ACTUAL AUDIO POSITION, not UI state
        // This was the root cause of the desync - using wrong reference angle
        
        // Store the touch angle for drag calculations
        lastFingerAngle = fingerAngle
        lastTouchTime = System.currentTimeMillis()
        compensationApplied = false

        // FIXED: Calculate the offset between finger position and ACTUAL AUDIO position
    val fingerToAudioOffset = fingerAngle - currentAudioAngle  // Use true audio angle, not UI

        // Normalize the offset to [-180, 180] range for shortest path
        var normalizedOffset = fingerToAudioOffset
        while (normalizedOffset > 180f) normalizedOffset -= 360f
        while (normalizedOffset < -180f) normalizedOffset += 360f

        // Store this offset for compensation during dragging
        angularOffset = normalizedOffset

    // Smooth hand-off: preserve the current UI angle at touch-down (no snap)
    // visuals are audioAngle + uiPhaseOffset, so set offset to currentUIAngle - audioAngle
    uiPhaseOffset = (currentUIAngle - currentAudioAngle)
    while (uiPhaseOffset > 180f) uiPhaseOffset -= 360f
    while (uiPhaseOffset < -180f) uiPhaseOffset += 360f

        // BACKSPIN DEBUG: Log offset calculation
    Log.i("BACKSPIN_DEBUG", "OFFSET_CALC_FIXED: fingerToAudioOffset=%.2f°, normalizedOffset=%.2f° (stored as angularOffset)".format(fingerToAudioOffset, normalizedOffset))

        // Start manual control with ZERO movement - do NOT jump to finger position
        onScratchPlatterActive(true, 0.0f)  // Start manual control with zero delta
    }    fun onPlatterDrag(angleDelta: Float, radialFactor: Float) {
        if (isPlatterTouched) {
            val now = System.currentTimeMillis()
            val prevTime = lastTouchTime.takeIf { it != 0L } ?: (now - 16L)
            val dtMs = (now - prevTime).coerceAtLeast(1L)
            val dtSeconds = dtMs / 1000f
            lastTouchTime = now

            // BACKSPIN DEBUG: Log drag movement and positions
            val beforeDragUIAngle = vinylAngle
            val beforeAudioAngle = try { onGetCurrentAudioAngleDegrees() } catch (_: Exception) { onGetCurrentTickBasedVinylAngle() }

            // Convert to per-60fps equivalent delta so native normalization is time-consistent
            // delta_per_frame_60 = (angleDelta / dtSeconds) / 60, then apply sensitivity factor
            var adjusted = (angleDelta / (dtSeconds * 60f)) * touchSensitivityFactor
            if (useRadiusAwareTouch) {
                // radialFactor is r/R in [0..1]; to keep 1:1 anywhere, divide by r/R
                // Protect against near-center spikes by clamping the inverse gain
                val rNorm = radialFactor.coerceIn(0.05f, 1.0f) // min radius 5% to avoid huge gain
                adjusted /= rNorm
            }
            val finalAngleDelta = adjusted
            // Visuals remain audio-driven to reflect slipmat physics; do not pre-adjust uiPhaseOffset here
            // Audio remains authoritative; send delta to engine

            // BACKSPIN DEBUG: Log after UI update, before audio update
            Log.i("BACKSPIN_DEBUG", "DRAG: angleDelta=%.2f°, UI_before=%.2f°, AUDIO_before=%.2f°".format(angleDelta, beforeDragUIAngle, beforeAudioAngle))

            // Send delta to audio system  
            onScratchPlatterActive(true, finalAngleDelta)
            
            // BACKSPIN DEBUG: Log audio position after update
            val afterAudioAngle = try { onGetCurrentAudioAngleDegrees() } catch (_: Exception) { onGetCurrentTickBasedVinylAngle() }
            Log.i("BACKSPIN_DEBUG", "DRAG_RESULT: AUDIO_after=%.2f°".format(afterAudioAngle))
        }
    }

    fun onPlatterTouchUp() {
        isPlatterTouched = false
        Log.i("HIGH_FREQ_VISUAL", "Touch UP - Returning to vsync visual updates")
        onReleasePlatterTouch()
    }

    fun onButtonPressStateChange(buttonId: Int, isPressed: Boolean) {
        // Update button states
        if (buttonId == 1) {
            button1Pressed = isPressed
            isButton1PhysicallyHeld = isPressed
        } else if (buttonId == 2) {
            button2Pressed = isPressed
            isButton2PhysicallyHeld = isPressed
        }
        
        if (!isPressed) {
            // Cancel hold jobs when button is released
            if (buttonId == 1) {
                button1HoldJob?.cancel()
                button1HoldJob = null
            } else if (buttonId == 2) {
                button2HoldJob?.cancel()
                button2HoldJob = null
            }
            return
        }
        
        // Simple single-button hold logic
        if (button1Pressed && !button2Pressed) {
            button1LongPressHandled = false  // Reset the flag when button is first pressed
            if (button1HoldJob?.isActive != true) {
                button1HoldJob = viewModelScope.launch {
                    delay(SAMPLE_LOAD_HOLD_DURATION_MS)
                    if (button1Pressed && !button2Pressed) {
                        Log.i("AppViewModel_Hold", "Button 1 held for sample load screen")
                        button1LongPressHandled = true  // Set flag when long press is detected
                        handleButton1Hold()
                    }
                }
            }
        } else if (button2Pressed && !button1Pressed) {
            button2LongPressHandled = false  // Reset the flag when button is first pressed
            if (button2HoldJob?.isActive != true) {
                button2HoldJob = viewModelScope.launch {
                    delay(SAMPLE_LOAD_HOLD_DURATION_MS)
                    if (button2Pressed && !button1Pressed) {
                        Log.i("AppViewModel_Hold", "Button 2 held for sample load screen")
                        button2LongPressHandled = true  // Set flag when long press is detected
                        handleButton2Hold()
                    }
                }
            }
        }
    }

    private fun saveUserSoundList(list: List<UserSoundItem>, key: String) {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val jsonArray = JSONArray()
        list.filter { !it.isHardcoded && it.saveToAutoLoad }.forEach { item ->
            val jsonObject = JSONObject()
            jsonObject.put("filePath", item.filePath)
            jsonObject.put("displayName", item.displayName)
            jsonObject.put("title", item.title ?: "")
            jsonObject.put("artist", item.artist ?: "")
            jsonObject.put("durationSeconds", item.durationSeconds)
            jsonObject.put("source", item.source.name) // Save source as string
            jsonArray.put(jsonObject)
        }
        prefs.edit { putString(key, jsonArray.toString()) }
    }

    private fun loadUserSoundList(key: String): MutableList<UserSoundItem> {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val jsonString = prefs.getString(key, null)
        val loadedList = mutableListOf<UserSoundItem>()
        if (jsonString != null) {
            try {
                val jsonArray = JSONArray(jsonString)
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    val filePath = jsonObject.getString("filePath")

                    // PRODUCTION SAFETY: Validate weekly tracks are not unreleased
                    if (filePath.startsWith("weekly/")) {
                        val weeklyTrack = tracks.value.find { track ->
                            "weekly/${track.filename}" == filePath
                        }
                        if (weeklyTrack?.isYetToBeReleased() == true) {
                            Log.w("AppViewModel_Persistence", "Skipping unreleased track from persistence: $filePath")
                            continue // Skip this unreleased track
                        }
                    }

                    loadedList.add(UserSoundItem(
                        filePath = filePath,
                        displayName = jsonObject.getString("displayName"),
                        saveToAutoLoad = true, // Items loaded from save were marked to be saved
                        isHardcoded = false,
                        title = jsonObject.optString("title").takeIf { it.isNotEmpty() },
                        artist = jsonObject.optString("artist").takeIf { it.isNotEmpty() },
                        durationSeconds = jsonObject.optInt("durationSeconds", 0),
                        source = try {
                            TrackSource.valueOf(jsonObject.optString("source", TrackSource.USER_ADDED.name))
                        } catch (e: IllegalArgumentException) {
                            TrackSource.USER_ADDED // Default fallback for invalid values
                        }
                    ))
                }
            } catch (e: Exception) {
                Log.e("AppViewModel_Persistence", "Error loading list for key $key", e)
            }
        }
        return loadedList
    }

    /**
     * Helper to extract MP3 metadata from URI (for user-added files)
     */
    private fun getMp3MetadataFromUri(uriString: String): Triple<String?, String?, Int> {
        var title: String? = null
        var artist: String? = null
        var duration = 0
        
        try {
            val uri = Uri.parse(uriString)
            val tempFile = File.createTempFile("meta_temp_", ".mp3", getApplication<Application>().cacheDir)
            
            getApplication<Application>().contentResolver.openInputStream(uri)?.use { inputStream ->
                tempFile.outputStream().use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }
            
            val mmr = MediaMetadataRetriever()
            mmr.setDataSource(tempFile.absolutePath)
            title = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_TITLE)
            artist = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST)
            duration = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toIntOrNull() ?: 0
            mmr.release()
            tempFile.delete()
            
        } catch (e: Exception) {
            Log.w("AppViewModel", "Could not read metadata from URI $uriString: ${e.message}")
        }
        
        return Triple(title, artist, duration / 1000) // duration in seconds
    }

    /**
     * Helper to detect if a track is an Ayo track by checking for URLs in MP3 comment metadata
     */
    private fun detectTrackSource(uriString: String): TrackSource {
        try {
            val uri = Uri.parse(uriString)
            val tempFile = File.createTempFile("source_detect_", ".mp3", getApplication<Application>().cacheDir)
            
            getApplication<Application>().contentResolver.openInputStream(uri)?.use { inputStream ->
                tempFile.outputStream().use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }
            
            // Use JAudioTagger to read comment metadata
            try {
                val audioFile = org.jaudiotagger.audio.AudioFileIO.read(tempFile)
                val tag = audioFile.tag
                val comment = tag?.getFirst(org.jaudiotagger.tag.FieldKey.COMMENT)
                
                tempFile.delete()
                
                Log.d("AppViewModel_Source", "Reading metadata for $uriString - Comment: '$comment'")
                
                // Check if comment contains a Google Drive URL (Ayo tracks have URLs in comments)
                if (!comment.isNullOrBlank() && (
                    comment.contains("drive.google.com", ignoreCase = true) ||
                    comment.contains("google.com", ignoreCase = true) ||
                    comment.contains("ayo", ignoreCase = true)
                )) {
                    Log.d("AppViewModel_Source", "Detected Ayo track: $uriString (has identifier in comment)")
                    return TrackSource.AYO_OFFICIAL
                } else {
                    Log.d("AppViewModel_Source", "Detected user track: $uriString (no identifier in comment)")
                }
            } catch (e: Exception) {
                Log.w("AppViewModel_Source", "Could not read ID3 comment from $uriString: ${e.message}")
                tempFile.delete()
            }
            
        } catch (e: Exception) {
            Log.w("AppViewModel_Source", "Could not detect source for $uriString: ${e.message}")
        }
        
        // Default to user-added if no URL found or error occurred
        return TrackSource.USER_ADDED
    }

    /**
     * Clean URI lists to remove duplicates and invalid entries
     */
    private fun cleanURILists() {
        Log.d("AppViewModel_Clean", "Starting URI list cleanup")
        
        // Clean platter samples list
        val originalPlatterSize = userPlatterSamplesList.size
        val cleanedPlatterList = userPlatterSamplesList.distinctBy { it.filePath }.toMutableList()
        userPlatterSamplesList.clear()
        userPlatterSamplesList.addAll(cleanedPlatterList)
        
        // Clean music tracks list  
        val originalMusicSize = userMusicTracksList.size
        val cleanedMusicList = userMusicTracksList.distinctBy { it.filePath }.toMutableList()
        userMusicTracksList.clear()
        userMusicTracksList.addAll(cleanedMusicList)
        
        Log.d("AppViewModel_Clean", "Platter samples: $originalPlatterSize -> ${userPlatterSamplesList.size}")
        Log.d("AppViewModel_Clean", "Music tracks: $originalMusicSize -> ${userMusicTracksList.size}")
        
        // Update path lists after cleaning
        updateActivePlaylist(SoundType.PLATTER_SAMPLE)
        updateActivePlaylist(SoundType.MUSIC_TRACK)
    }

    /**
     * Re-detect sources for existing tracks that might not have proper source identification
     */
    private fun redetectSourcesForExistingTracks() {
        viewModelScope.launch(Dispatchers.IO) {
            // Check music tracks for source detection
            Log.d("AppViewModel_Source", "Starting source detection for ${userMusicTracksList.size} music tracks")
            userMusicTracksList.forEachIndexed { index, item ->
                Log.d("AppViewModel_Source", "Track $index: ${item.displayName} - current source: ${item.source}, isHardcoded: ${item.isHardcoded}, path: ${item.filePath}")
                
                if (item.source == TrackSource.USER_ADDED && 
                    !item.isHardcoded && 
                    item.filePath.startsWith("content://") &&
                    item.filePath.endsWith(".mp3", ignoreCase = true)) {
                    
                    Log.d("AppViewModel_Source", "Running detection for track: ${item.displayName}")
                    try {
                        val detectedSource = detectTrackSource(item.filePath)
                        Log.d("AppViewModel_Source", "Detection result for ${item.displayName}: $detectedSource")
                        if (detectedSource == TrackSource.AYO_OFFICIAL) {
                            withContext(Dispatchers.Main) {
                                userMusicTracksList[index] = item.copy(source = TrackSource.AYO_OFFICIAL)
                                Log.d("AppViewModel_Source", "Updated source for existing track: ${item.displayName}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.w("AppViewModel_Source", "Error re-detecting source for ${item.displayName}: ${e.message}")
                    }
                } else {
                    Log.d("AppViewModel_Source", "Skipping detection for ${item.displayName} - conditions not met")
                }
            }
            
            // Save updated sources
            withContext(Dispatchers.Main) {
                saveUserSoundList(userMusicTracksList, KEY_MUSIC_TRACKS)
            }
        }
    }

    /**
     * Centralized file loading logic for consistent handling across both channels
     */
    private suspend fun loadFileConsistently(
        filePath: String, 
        soundType: SoundType,
        userSoundItem: UserSoundItem? = null
    ) {
        when {
            filePath.startsWith("content://") -> {
                // User content URI - use modification timestamp for cache validation
                val modificationTimestamp = userSoundItem?.modificationTimestamp ?: System.currentTimeMillis()
                if (soundType == SoundType.PLATTER_SAMPLE) {
                    onLoadUserPlatterSample(filePath, modificationTimestamp)
                } else {
                    onLoadUserMusicTrack(filePath, modificationTimestamp)
                }
            }
            filePath.startsWith("file://") -> {
                // File URI - convert to user loading with default timestamp
                val modificationTimestamp = userSoundItem?.modificationTimestamp ?: System.currentTimeMillis()
                if (soundType == SoundType.PLATTER_SAMPLE) {
                    onLoadUserPlatterSample(filePath, modificationTimestamp)
                } else {
                    onLoadUserMusicTrack(filePath, modificationTimestamp)
                }
            }
            filePath.startsWith("weekly/") || filePath.startsWith("sounds/") -> {
                // Asset paths - use direct asset loading
                if (soundType == SoundType.PLATTER_SAMPLE) {
                    onLoadAssetPlatterSample(filePath)
                } else {
                    onLoadAssetMusicTrack(filePath)
                }
            }
            filePath.startsWith("/") -> {
                // Absolute file path - convert to file URI and load as user content
                val fileUri = "file://$filePath"
                val modificationTimestamp = userSoundItem?.modificationTimestamp ?: System.currentTimeMillis()
                if (soundType == SoundType.PLATTER_SAMPLE) {
                    onLoadUserPlatterSample(fileUri, modificationTimestamp)
                } else {
                    onLoadUserMusicTrack(fileUri, modificationTimestamp)
                }
            }
            else -> {
                // Fallback - treat as asset path
                if (soundType == SoundType.PLATTER_SAMPLE) {
                    onLoadAssetPlatterSample(filePath)
                } else {
                    onLoadAssetMusicTrack(filePath)
                }
            }
        }
    }

    fun addSoundItem(uriString: String, displayName: String, type: SoundType, modTime: Long): String? {
        val list = if (type == SoundType.PLATTER_SAMPLE) userPlatterSamplesList else userMusicTracksList

        // Extract metadata for music files first
        val (title, artist, duration) = if (type == SoundType.MUSIC_TRACK && uriString.endsWith(".mp3", ignoreCase = true)) {
            getMp3MetadataFromUri(uriString)
        } else {
            Triple(null, null, 0)
        }

        // Detect track source for music files
        val trackSource = if (type == SoundType.MUSIC_TRACK && uriString.endsWith(".mp3", ignoreCase = true)) {
            detectTrackSource(uriString)
        } else {
            TrackSource.USER_ADDED // Non-MP3 or platter samples default to user-added
        }

        // Create the new item for duplicate checking
        val newItem = UserSoundItem(
            filePath = uriString,
            displayName = displayName,
            saveToAutoLoad = false,
            isHardcoded = false,
            modificationTimestamp = modTime,
            title = title,
            artist = artist,
            durationSeconds = duration,
            source = trackSource
        )

        // Check for duplicates using improved metadata-based detection
        if (type == SoundType.MUSIC_TRACK && isDuplicateTrack(newItem, list)) {
            Log.w("AppViewModel_Files", "Attempted to add duplicate track based on metadata: $displayName")
            return "$displayName is already in the list (duplicate detected by metadata)."
        } else if (list.any { it.filePath == uriString }) {
            Log.w("AppViewModel_Files", "Attempted to add duplicate file path: $uriString")
            return "$displayName is already in the list."
        }

        val userItems = list.filter { !it.isHardcoded }
        
        if (userItems.size < MAX_USER_SOUNDS) {
            list.add(newItem)
            // Log.d("AppViewModel_Files", "Added $displayName (Path: $uriString) to $type UI list.")
        } else {
            val replaceableIndexInFullList = list.indexOfFirst { !it.isHardcoded && !it.saveToAutoLoad }
            if (replaceableIndexInFullList != -1) {
                list[replaceableIndexInFullList] = newItem
            } else {
                Log.w("AppViewModel_Files", "$type list is full and all items are marked for save.")
                return "List is full. Unselect 'save' on an existing item to replace it."
            }
        }

        // Save playlist if this is a music track
        if (type == SoundType.MUSIC_TRACK) {
            savePlaylistToPersistence()
        }
        
        // Sort and update effective playback paths 
        list.sortWith(compareBy<UserSoundItem> { it.isHardcoded }.thenBy { it.displayName })
        updateActivePlaylist(type)

        if (audioLoadingState is AudioLoadingState.Loading) {
            return "Busy, new sound added to list but not loaded. Try selecting it manually if needed."
        }

        audioLoadingState = AudioLoadingState.Loading("Loading $displayName...", isBackgroundOperation = true)
        viewModelScope.launch(Dispatchers.IO) {
            executeLoadingOperation {
                var newPlatterIndexOnMain = currentPlatterSampleIndex
                var newMusicIndexOnMain = currentMusicTrackIndex
                var tempPlatterPaths: List<String>? = null // To hold paths at the moment of IO dispatch
                var tempMusicPaths: List<String>? = null   // To hold paths at the moment of IO dispatch
                try {
                    // Check cache first to avoid redundant loading
                    if (isRecentlyLoaded(uriString)) {
                        Log.d("AppViewModel_AddSound", "Track $uriString recently loaded, skipping redundant load")
                        withContext(Dispatchers.Main) {
                            audioLoadingState = AudioLoadingState.Idle
                        }
                        return@executeLoadingOperation
                    }

                    // Capture the state of paths *before* IO, as `list` might change if user interacts rapidly
                    if (type == SoundType.PLATTER_SAMPLE) {
                        tempPlatterPaths = userPlatterSamplesList.map { it.filePath }
                        // Use centralized loading logic
                        loadFileConsistently(uriString, SoundType.PLATTER_SAMPLE, newItem)
                        newPlatterIndexOnMain = tempPlatterPaths.indexOf(uriString).takeIf { it != -1 } ?: 0
                    } else {
                        tempMusicPaths = userMusicTracksList.map { it.filePath }
                        // Use centralized loading logic
                        loadFileConsistently(uriString, SoundType.MUSIC_TRACK, newItem)
                        newMusicIndexOnMain = tempMusicPaths.indexOf(uriString).takeIf { it != -1 } ?: 0
                    }

                    // Mark as recently loaded after successful load
                    markAsRecentlyLoaded(uriString)

                    withContext(Dispatchers.Main) {
                        if (type == SoundType.PLATTER_SAMPLE) {
                            <EMAIL> = tempPlatterPaths ?: <EMAIL> // Use captured paths
                            currentPlatterSampleIndex = newPlatterIndexOnMain
                        } else {
                            <EMAIL> = tempMusicPaths ?: <EMAIL> // Use captured paths
                            currentMusicTrackIndex = newMusicIndexOnMain
                        }
                    }
                } catch (e: Exception) {
                    val errorMsg = "Error loading $displayName"
                    Log.e("AppViewModel_AddSound", "$errorMsg: ${e.message}", e)
                    withContext(Dispatchers.Main) {
                        audioLoadingState = AudioLoadingState.Error(errorMsg, e.message)
                        // Paths might still need update from the add operation before IO if an error occurred during load
                        if (type == SoundType.PLATTER_SAMPLE && tempPlatterPaths != null) {
                            <EMAIL> = tempPlatterPaths
                        } else if (type == SoundType.MUSIC_TRACK && tempMusicPaths != null) {
                            <EMAIL> = tempMusicPaths
                        }
                    }
                } finally {
                    if (audioLoadingState !is AudioLoadingState.Error) {
                        audioLoadingState = AudioLoadingState.Idle
                    }
                }
            }
        }
        return null // Assuming success for now for adding to list, load is async
    }

    fun toggleSaveForSoundItem(index: Int, type: SoundType) {
        val list = if (type == SoundType.PLATTER_SAMPLE) userPlatterSamplesList else userMusicTracksList
        if (index >= 0 && index < list.size) {
            val item = list[index]
            // Check subscription status - FREE users cannot modify URI list at all
            val subscriptionStatus = trackManager.getSubscriptionStatus()
            if (subscriptionStatus == SubscriptionStatus.FREE) {
                showUpgradePrompt()
                return
            }

            // Allow all subscription tiers (except FREE) to toggle items
            // Premium users can toggle hardcoded items, others cannot
            if (!item.isHardcoded || subscriptionStatus == SubscriptionStatus.FULL_PREMIUM) {
                val updatedItem = item.copy(saveToAutoLoad = !item.saveToAutoLoad)
                list[index] = updatedItem

                // Immediately update the active playlist to reflect the change
                updateActivePlaylist(type)

                // Save playlist if this is a music track
                if (type == SoundType.MUSIC_TRACK) {
                    savePlaylistToPersistence()
                }

                // Save hardcoded selection states if this is a hardcoded item
                if (updatedItem.isHardcoded) {
                    saveHardcodedSelectionStates()
                }
            } else {
                Log.d("AppViewModel_Checkbox", "Non-premium user attempted to toggle hardcoded item ${item.displayName} - showing upgrade prompt")
                showUpgradePrompt()
            }
        } else {
            Log.e("AppViewModel_Checkbox", "toggleSaveForSoundItem: Index $index out of bounds for $type list size ${list.size}")
        }
    }

    private fun updateActivePlaylist(type: SoundType) {
        val list = if (type == SoundType.PLATTER_SAMPLE) userPlatterSamplesList else userMusicTracksList
        val key = if (type == SoundType.PLATTER_SAMPLE) KEY_PLATTER_SAMPLES else KEY_MUSIC_TRACKS

        // Filter to get only items that should be active (both hardcoded and user items with saveToAutoLoad=true)
        val activeItems = list.filter { it.saveToAutoLoad }
        val effectivePlaybackPaths = activeItems.map { it.filePath }

        Log.d("AppViewModel_Playlist", "updateActivePlaylist($type): ${activeItems.size} active items from ${list.size} total")

        if (type == SoundType.PLATTER_SAMPLE) {
            this.platterSamplePaths = effectivePlaybackPaths
            if (currentPlatterSampleIndex >= this.platterSamplePaths.size) {
                Log.d("AppViewModel_Playlist", "Platter index reset: $currentPlatterSampleIndex -> 0 (size: ${this.platterSamplePaths.size})")
                currentPlatterSampleIndex = 0
            }
        } else {
            this.musicTrackPaths = effectivePlaybackPaths
            if (currentMusicTrackIndex >= this.musicTrackPaths.size) {
                Log.d("AppViewModel_Playlist", "Music index reset: $currentMusicTrackIndex -> 0 (size: ${this.musicTrackPaths.size})")
                currentMusicTrackIndex = 0
            }
        }
        
        // Save the current state
        saveUserSoundList(list, key)
    }


    fun processLoadAction(type: SoundType): String {
        val list = if (type == SoundType.PLATTER_SAMPLE) userPlatterSamplesList else userMusicTracksList
        val key = if (type == SoundType.PLATTER_SAMPLE) KEY_PLATTER_SAMPLES else KEY_MUSIC_TRACKS

        // Filter out non-saved user items, keep only items marked for saving (both hardcoded and user items)
        val itemsToKeep = list.filter { it.saveToAutoLoad }

        list.clear()
        list.addAll(itemsToKeep.sortedWith(compareBy<UserSoundItem> { it.isHardcoded }.thenBy { it.displayName }))

        saveUserSoundList(list, key) // Save the filtered list (only items marked for saving)

        // Save playlist if this is a music track
        if (type == SoundType.MUSIC_TRACK) {
            savePlaylistToPersistence()
        }

        // Update the effective playback paths - only include selected items
        val effectivePlaybackPaths = itemsToKeep.map { it.filePath }
        if (type == SoundType.PLATTER_SAMPLE) {
            this.platterSamplePaths = effectivePlaybackPaths
            // Optionally, reset currentPlatterSampleIndex or ensure it's valid
            if (currentPlatterSampleIndex >= this.platterSamplePaths.size) currentPlatterSampleIndex = 0
            Log.d("AppViewModel_Files", "Updated platterSamplePaths for playback: ${this.platterSamplePaths.joinToString()}")
        } else {
            this.musicTrackPaths = effectivePlaybackPaths
            if (currentMusicTrackIndex >= this.musicTrackPaths.size) currentMusicTrackIndex = 0
            // Log.d("AppViewModel_Files", "Updated musicTrackPaths for playback: ${this.musicTrackPaths.joinToString()}")
        }

        if (type == SoundType.PLATTER_SAMPLE) showPlatterSampleLoadPage = false else showMusicTrackLoadPage = false
        return "${
            type.name.lowercase(Locale.ROOT)
                .replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.ROOT) else it.toString() }} list updated and saved."
    }

    fun cancelLoadAction(type: SoundType) {
        val listToReset = if (type == SoundType.PLATTER_SAMPLE) userPlatterSamplesList else userMusicTracksList
        val key = if (type == SoundType.PLATTER_SAMPLE) KEY_PLATTER_SAMPLES else KEY_MUSIC_TRACKS

        listToReset.clear()
        // Load user items from prefs
        val loadedUserItems = loadUserSoundList(key)
        listToReset.addAll(loadedUserItems)

        // Add default hardcoded items if they aren't in the loadedUserItems (they shouldn't be, as loadUserSoundList filters for non-hardcoded)
        val ayoFeatsAssetPaths = if (type == SoundType.PLATTER_SAMPLE) {
            listOf("sounds/sample1", "sounds/sample2")
        } else {
            listOf("tracks/trackA", "tracks/trackB")
        }

        ayoFeatsAssetPaths.forEach { path ->
            if (!listToReset.any { it.filePath == path }) { // Ensure not to add duplicates if somehow saved
                val ayoFeatsName = when (path) {
                    "sounds/sample1" -> "Vinyl Scratch Classic - Ayo Feats!"
                    "sounds/sample2" -> "Turntable Thunder - Ayo Feats!"
                    "tracks/trackA" -> "Original Mix - Ayo Feats!"
                    "tracks/trackB" -> "Scratch Foundation - Ayo Feats!"
                    else -> path.substringAfterLast('/') + " - Ayo Feats!"
                }
                listToReset.add(UserSoundItem(
                    filePath = path,
                    displayName = ayoFeatsName,
                    saveToAutoLoad = getHardcodedSelectionState(path, type),
                    isHardcoded = true
                ))
            }
        }
        listToReset.sortWith(compareBy<UserSoundItem> { it.isHardcoded }.thenBy { it.displayName })

        // Update effective playback paths
        val effectivePlaybackPaths = listToReset.map { it.filePath }
        if (type == SoundType.PLATTER_SAMPLE) {
            this.platterSamplePaths = effectivePlaybackPaths
            if (currentPlatterSampleIndex >= this.platterSamplePaths.size) currentPlatterSampleIndex = 0
        } else {
            this.musicTrackPaths = effectivePlaybackPaths
            if (currentMusicTrackIndex >= this.musicTrackPaths.size) currentMusicTrackIndex = 0
        }

        // Log.d("AppViewModel_Files", "Cancelled $type loading. List reset from persistence and defaults. Effective paths: ${effectivePlaybackPaths.joinToString()}")
        if (type == SoundType.PLATTER_SAMPLE) showPlatterSampleLoadPage = false else showMusicTrackLoadPage = false
    }
    
    // === MONETIZATION FUNCTIONS ===
    
    private fun initializeMonetizationSystem() {
        viewModelScope.launch {
            try {
                // Initialize first run if needed
                MonthlyUpdateManager.initializeFirstRun(getApplication())
                
                // Check for monthly updates
                if (MonthlyUpdateManager.isMonthlyUpdateNeeded(getApplication())) {
                    MonthlyUpdateService.startMonthlyCheck(getApplication())
                }
                
                // Check if trial info popup should be shown
                checkAndShowTrialInfo()
                
                Log.d("AppViewModel", "Monetization system initialized")
            } catch (e: Exception) {
                Log.e("AppViewModel", "Failed to initialize monetization system", e)
            }
        }
    }
    
    /**
     * Check if trial info popup should be shown to new users
     */
    private fun checkAndShowTrialInfo() {
        val trialManager = trackManager.getTrialManager()
        if (trialManager.shouldShowTrialInfo()) {
            showTrialInfoPopup = true
        }
    }
    
    fun showTracksList() {
        showTrackList = true
    }
    
    fun hideTracksList() {
        showTrackList = false
    }
    
    /**
     * Check if there are any new tracks that should trigger notification
     */
    private fun checkForNewTrackNotifications() {
        val currentTracks = trackManager.visibleTracks.value
        val unlockedTracks = currentTracks.filter { it.isUnlocked }

        // Only show notification for tracks that:
        // 1. User hasn't seen yet (not in seenTrackIds)
        // 2. Were released AFTER the last notification dismissal
        val newTracks = unlockedTracks.filter { track ->
            !seenTrackIds.contains(track.id) && track.releaseTimestamp > lastNotificationDismissalTime
        }

        hasNewTrackNotification = newTracks.isNotEmpty()

        Log.d("AppViewModel_Notifications", "Total unlocked: ${unlockedTracks.size}, New since dismissal: ${newTracks.size}, Last dismissal: $lastNotificationDismissalTime, Notification: $hasNewTrackNotification")
    }
    
    private fun loadSeenTrackIds() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val seenIds = prefs.getStringSet(KEY_SEEN_TRACK_IDS, emptySet()) ?: emptySet()
        seenTrackIds.addAll(seenIds)
        Log.d("AppViewModel_Notifications", "Loaded ${seenIds.size} seen track IDs")
    }
    
    private fun saveSeenTrackIds() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit { putStringSet(KEY_SEEN_TRACK_IDS, seenTrackIds) }
        Log.d("AppViewModel_Notifications", "Saved ${seenTrackIds.size} seen track IDs")
    }
    
    private fun startEmojiRotation() {
        viewModelScope.launch {
            while (true) {
                delay(2000) // Change emoji every 2 seconds
                currentEmojiIndex = (currentEmojiIndex + 1) % notificationEmojis.size
                currentNotificationEmoji = notificationEmojis[currentEmojiIndex]
            }
        }
    }
    
    // Track when notification was last dismissed to prevent showing for old tracks
    private var lastNotificationDismissalTime: Long = 0L

    /**
     * Load notification state from persistent storage
     */
    private fun loadNotificationState() {
        val prefs = getApplication<Application>().getSharedPreferences("notification_state", Context.MODE_PRIVATE)
        lastNotificationDismissalTime = prefs.getLong("last_dismissal_time", 0L)
        Log.d("AppViewModel_Notifications", "Loaded last dismissal time: $lastNotificationDismissalTime")
    }

    /**
     * Save notification dismissal time to persistent storage
     */
    private fun saveNotificationDismissalTime() {
        val prefs = getApplication<Application>().getSharedPreferences("notification_state", Context.MODE_PRIVATE)
        lastNotificationDismissalTime = System.currentTimeMillis()
        prefs.edit().putLong("last_dismissal_time", lastNotificationDismissalTime).apply()
        Log.d("AppViewModel_Notifications", "Saved dismissal time: $lastNotificationDismissalTime")
    }

    /**
     * Dismiss new track notification and mark tracks as seen
     */
    private fun dismissNewTrackNotification(reason: String) {
        if (hasNewTrackNotification) {
            val currentTracks = trackManager.visibleTracks.value
            val unlockedTracks = currentTracks.filter { it.isUnlocked }
            unlockedTracks.forEach { track ->
                seenTrackIds.add(track.id)
            }
            saveSeenTrackIds()
            hasNewTrackNotification = false
            saveNotificationDismissalTime() // Save when notification was dismissed
            Log.d("AppViewModel_Notifications", "Dismissed notification ($reason), marked ${unlockedTracks.size} tracks as seen")
        }
    }

    fun onNewTrackNotificationClicked() {
        dismissNewTrackNotification("user clicked icon")

        // Show track list
        showTrackList = true
    }
    
    // === DEV MODE FUNCTIONS ===
    
    private fun loadDevModeState() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        isDevModeEnabled = prefs.getBoolean(KEY_DEV_MODE, false)
        
        // Apply dev mode state to MainActivity for compatibility
        val currentStatus = trackManager.getSubscriptionStatus()
        MainActivity.isCurrentUserPremium = currentStatus.hasFullAccess()
        
        // Refresh subscription status to apply dev mode state
        trackManager.updateSubscriptionStatus()
        
        Log.d("AppViewModel_DevMode", "Loaded dev mode state: $isDevModeEnabled - subscription status: $currentStatus")
    }
    
    private fun saveDevModeState() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit { putBoolean(KEY_DEV_MODE, isDevModeEnabled) }
        Log.d("AppViewModel_DevMode", "Saved dev mode state: $isDevModeEnabled")
    }
    
    fun toggleDevMode() {
        isDevModeEnabled = !isDevModeEnabled
        
        // Save state to preferences first
        saveDevModeState()
        
        // Apply dev mode effects
        viewModelScope.launch {
            // Force subscription status refresh - our new getSubscriptionStatus() will handle dev mode automatically
            trackManager.updateSubscriptionStatus()
            
            // Update MainActivity for compatibility with existing code (will be removed later)
            val currentStatus = trackManager.getSubscriptionStatus()
            MainActivity.isCurrentUserPremium = currentStatus.hasFullAccess()
            
            // Refresh playlists to apply subscription changes
            updateMusicLibraryWithWeeklyTracks()
            validateAndCleanPlaylists()
            
            if (isDevModeEnabled) {
                Log.i("AppViewModel_DevMode", "Dev mode enabled - subscription status: $currentStatus")
            } else {
                Log.i("AppViewModel_DevMode", "Dev mode disabled - subscription status: $currentStatus")
            }
        }
    }
    
    fun playTrack(track: Track) {
        viewModelScope.launch {
            try {
                // CRITICAL: Validate track is actually released before allowing playback
                if (track.isYetToBeReleased()) {
                    Log.w("AppViewModel", "Attempted to play unreleased track: ${track.title}")
                    return@launch
                }
                
                if (track.localFilePath != null) {
                    // Load local saved file
                    Log.i("AppViewModel", "Playing local track: ${track.localFilePath}")
                    if (track.localFilePath!!.startsWith("content://")) {
                        // MediaStore content URI (Android 10+) - treat as user music track
                        val userSoundItem = userMusicTracksList.find { it.filePath == track.localFilePath }
                        if (userSoundItem != null) {
                            onLoadUserMusicTrack(track.localFilePath!!, userSoundItem.modificationTimestamp)
                        } else {
                            // Create a temporary UserSoundItem for saved tracks not yet in playlist
                            val tempTimestamp = System.currentTimeMillis()
                            onLoadUserMusicTrack(track.localFilePath!!, tempTimestamp)
                        }
                    } else if (track.localFilePath!!.startsWith("file://")) {
                        // File URI - convert to user music track
                        val userSoundItem = userMusicTracksList.find { it.filePath == track.localFilePath }
                        if (userSoundItem != null) {
                            onLoadUserMusicTrack(track.localFilePath!!, userSoundItem.modificationTimestamp)
                        } else {
                            val tempTimestamp = System.currentTimeMillis()
                            onLoadUserMusicTrack(track.localFilePath!!, tempTimestamp)
                        }
                    } else if (track.localFilePath!!.startsWith("/") || track.localFilePath!!.contains("Music/AyoDJ")) {
                        // Regular file path in public storage - convert to file URI
                        val userSoundItem = userMusicTracksList.find { it.filePath == track.localFilePath }
                        if (userSoundItem != null) {
                            onLoadUserMusicTrack(track.localFilePath!!, userSoundItem.modificationTimestamp)
                        } else {
                            // For regular file paths, try to load directly
                            val file = File(track.localFilePath!!)
                            if (file.exists()) {
                                val fileUri = "file://${track.localFilePath}"
                                val tempTimestamp = System.currentTimeMillis()
                                onLoadUserMusicTrack(fileUri, tempTimestamp)
                            } else {
                                Log.e("AppViewModel", "Saved track file does not exist: ${track.localFilePath}")
                            }
                        }
                    } else {
                        // Asset file path
                        onLoadAssetMusicTrack(track.localFilePath!!)
                    }
                    isMusicPlaying = true
                    dismissNewTrackNotification("track played from list")
                } else {
                    // CRITICAL: For remote tracks, only allow preview if actually released
                    if (track.isYetToBeReleased()) {
                        Log.w("AppViewModel", "Cannot preview unreleased track: ${track.title}")
                        showUpgradePrompt()
                        return@launch
                    }
                    
                    // For remote tracks, preview from assets or show download prompt
                    Log.i("AppViewModel", "Playing preview for track: ${track.title}")
                    val assetPath = "weekly/${track.filename}"
                    try {
                        onLoadAssetMusicTrack(assetPath)
                        isMusicPlaying = true
                        dismissNewTrackNotification("track played from list")
                    } catch (e: Exception) {
                        Log.w("AppViewModel", "Preview not available for ${track.title}, showing save prompt")
                        showUpgradePrompt()
                    }
                }
            } catch (e: Exception) {
                Log.e("AppViewModel", "Failed to play track: ${track.title}", e)
            }
        }
    }
    
    fun saveTrack(track: Track) {
        viewModelScope.launch {
            try {
                val success = trackManager.saveTrackToDevice(track.id)
                if (success) {
                    Log.i("AppViewModel", "Track saved: ${track.title}")
                    
                    // Add the downloaded track to the music playlist
                    addSavedTrackToPlaylist(track)
                    
                    // Could show a toast or success message here in the future
                } else {
                    Log.w("AppViewModel", "Failed to save track or requires premium")
                    // Check if it's a premium requirement or missing download URL
                    val downloadError = trackManager.getDownloadError(track.id)
                    if (downloadError?.contains("download not available", ignoreCase = true) == true ||
                        downloadError?.contains("no download URL", ignoreCase = true) == true) {
                        // This is a missing URL error - already handled by TrackManager
                        Log.i("AppViewModel", "Download not available for track: ${track.title}")
                    } else {
                        // This is a premium requirement
                        showUpgradePrompt()
                    }
                }
            } catch (e: Exception) {
                Log.e("AppViewModel", "Error saving track: ${track.title}", e)
            }
        }
    }
    
    /**
     * Add a successfully saved track to the music playlist
     */
    private suspend fun addSavedTrackToPlaylist(track: Track) {
        // PRODUCTION SAFETY: Validate track is actually released
        if (track.isYetToBeReleased()) {
            Log.w("AppViewModel", "Cannot add unreleased track to playlist: ${track.title}")
            return
        }

        // Get the updated track with local file path
        val updatedTrack = trackManager.getTrackById(track.id)
        if (updatedTrack?.localFilePath == null) {
            Log.w("AppViewModel", "Cannot add track to playlist: no local file path")
            return
        }
        
        // Create a UserSoundItem for the playlist  
        val userSoundItem = UserSoundItem(
            filePath = updatedTrack.localFilePath!!, // Use the actual saved path (content URI or file path)
            displayName = "${updatedTrack.artist} - ${updatedTrack.title}",
            saveToAutoLoad = true, // Auto-add to active playlist
            isHardcoded = false, // User-saved track
            modificationTimestamp = System.currentTimeMillis(),
            title = updatedTrack.title,
            artist = updatedTrack.artist,
            durationSeconds = updatedTrack.durationSeconds,
            source = TrackSource.AYO_OFFICIAL // Saved tracks from track manager are Ayo official
        )
        
        // Check if track is already in the playlist (avoid duplicates based on metadata)
        if (!isDuplicateTrack(userSoundItem, userMusicTracksList)) {
            // Add to the playlist
            userMusicTracksList.add(userSoundItem)

            // Update the active playlist and save to persistence
            updateActivePlaylist(SoundType.MUSIC_TRACK)
            savePlaylistToPersistence()

            Log.i("AppViewModel", "Added track to playlist: ${updatedTrack.title}")
        } else {
            Log.d("AppViewModel", "Track already in playlist (duplicate detected): ${updatedTrack.title}")
        }
    }
    
    fun showUpgradePrompt() {
        showSubscribePopup = true
    }
    
    fun upgradeToPremium() {
        viewModelScope.launch {
            try {
                // TODO: Integrate with actual payment system (Google Play Billing)
                // For now, this is a placeholder that would handle successful purchase
                val subscriptionPrefs = getApplication<Application>().getSharedPreferences("subscription_prefs", Context.MODE_PRIVATE)
                subscriptionPrefs.edit()
                    .putBoolean("has_monthly_subscription", true)
                    .apply()
                
                // Refresh subscription status
                trackManager.updateSubscriptionStatus()
                showSubscribePopup = false
                Log.i("AppViewModel", "Upgraded to premium")
            } catch (e: Exception) {
                Log.e("AppViewModel", "Failed to upgrade subscription", e)
            }
        }
    }

    /**
     * Update music library with current batch tracks
     */
    private fun updateMusicLibraryWithWeeklyTracks() {
        // CRITICAL: Filter by both unlocked AND actually released (not future)
        val currentBatchTracks = tracks.value.filter { 
            it.isUnlocked && !it.isYetToBeReleased() 
        }
        
        // Remove existing batch tracks from the list
        userMusicTracksList.removeAll { it.filePath.contains(Regex("""\d{3}-\d{2}-\d{2}[ab]\.mp3""")) }
        
        // Add current batch tracks, sorted by newest first
        currentBatchTracks.sortedByDescending { it.getSortingKey() }.forEach { track ->
            val trackPath = track.localFilePath ?: "weekly/${track.filename}"
            userMusicTracksList.add(0, UserSoundItem( // Add at beginning for newest first
                filePath = trackPath,
                displayName = "${track.title} - ${track.artist}",
                saveToAutoLoad = true,
                isHardcoded = false,
                title = track.title,
                artist = track.artist,
                durationSeconds = track.durationSeconds,
                source = TrackSource.AYO_OFFICIAL // Weekly tracks are Ayo official content
            ))
        }
        
        // Update effective paths for audio playback
        this.musicTrackPaths = userMusicTracksList.map { it.filePath }
        
        // Save updated list to preferences
        saveUserSoundList(userMusicTracksList, KEY_MUSIC_TRACKS)
    }

    /**
     * PRODUCTION SAFETY: Clean any unreleased tracks from active playlists
     * Call this periodically to ensure production readiness
     */
    private fun validateAndCleanPlaylists() {
        // Clean music tracks list
        val originalMusicSize = userMusicTracksList.size
        userMusicTracksList.removeAll { userSoundItem ->
            // Check if this is a weekly track that hasn't been released yet
            if (userSoundItem.filePath.startsWith("weekly/")) {
                val weeklyTrack = tracks.value.find { track ->
                    "weekly/${track.filename}" == userSoundItem.filePath
                }
                if (weeklyTrack?.isYetToBeReleased() == true) {
                    Log.w("AppViewModel", "Removing unreleased track from music playlist: ${userSoundItem.displayName}")
                    return@removeAll true
                }
            }
            false
        }

        // Clean platter samples list (though weekly tracks shouldn't be in platter samples)
        val originalPlatterSize = userPlatterSamplesList.size
        userPlatterSamplesList.removeAll { userSoundItem ->
            // Check if this is a weekly track that hasn't been released yet
            if (userSoundItem.filePath.startsWith("weekly/")) {
                val weeklyTrack = tracks.value.find { track ->
                    "weekly/${track.filename}" == userSoundItem.filePath
                }
                if (weeklyTrack?.isYetToBeReleased() == true) {
                    Log.w("AppViewModel", "Removing unreleased track from platter samples: ${userSoundItem.displayName}")
                    return@removeAll true
                }
            }
            false
        }

        // Update paths if any tracks were removed
        if (userMusicTracksList.size != originalMusicSize) {
            Log.i("AppViewModel", "Cleaned ${originalMusicSize - userMusicTracksList.size} unreleased tracks from music playlist")
            updateActivePlaylist(SoundType.MUSIC_TRACK)
        }
        if (userPlatterSamplesList.size != originalPlatterSize) {
            Log.i("AppViewModel", "Cleaned ${originalPlatterSize - userPlatterSamplesList.size} unreleased tracks from platter samples")
            updateActivePlaylist(SoundType.PLATTER_SAMPLE)
        }
    }

    /**
     * Initialize load page state to show only unlocked/saved items (same as after cancel)
     */
    private fun initializeLoadPageState(type: SoundType) {
        // Use existing runtime lists instead of rebuilding to maintain consistency
        val activePathList = if (type == SoundType.PLATTER_SAMPLE) platterSamplePaths else musicTrackPaths
        
        // SAFETY: Ensure hardcoded "Ayo Feats" items are always present in load screen
        ensureHardcodedItemsPresent(type)
        
        Log.d("AppViewModel_LoadPage", "Initialized ${type.name} load page with ${
            if (type == SoundType.PLATTER_SAMPLE) userPlatterSamplesList.size else userMusicTracksList.size
        } items (including unlocked weekly tracks). Active paths: ${activePathList.joinToString { it.substringAfterLast('/') }}")
        
        // No need to rebuild lists - they are already properly maintained in runtime
        // The load screen will use the existing userPlatterSamplesList or userMusicTracksList
        // which are the same lists used for cycling
    }

    /**
     * SAFETY: Ensure hardcoded "Ayo Feats" items are always present in the load screen
     * even if they were somehow removed or deselected
     */
    private fun ensureHardcodedItemsPresent(type: SoundType) {
        if (type == SoundType.PLATTER_SAMPLE) {
            // Ensure Ayo Feats platter samples are present
            val ayoFeatsPlatterPaths = listOf("sounds/sample1", "sounds/sample2")
            ayoFeatsPlatterPaths.forEach { path ->
                if (!userPlatterSamplesList.any { it.filePath == path }) {
                    val ayoFeatsName = when (path) {
                        "sounds/sample1" -> "Vinyl Scratch Classic - Ayo Feats!"
                        "sounds/sample2" -> "Turntable Thunder - Ayo Feats!"
                        else -> path.substringAfterLast('/') + " - Ayo Feats!"
                    }
                    userPlatterSamplesList.add(UserSoundItem(
                        filePath = path,
                        displayName = ayoFeatsName,
                        saveToAutoLoad = getHardcodedSelectionState(path, SoundType.PLATTER_SAMPLE),
                        isHardcoded = true,
                        source = TrackSource.AYO_OFFICIAL
                    ))
                    Log.d("AppViewModel_LoadPage", "Re-added missing hardcoded platter sample: $ayoFeatsName")
                }
            }
        } else if (type == SoundType.MUSIC_TRACK) {
            // Ensure Ayo Feats music tracks are present
            val ayoFeatsMusicPaths = listOf("sounds/music1", "sounds/music2")
            ayoFeatsMusicPaths.forEach { path ->
                if (!userMusicTracksList.any { it.filePath == path }) {
                    val ayoFeatsName = when (path) {
                        "sounds/music1" -> "Vinyl Groove Classic - Ayo Feats!"
                        "sounds/music2" -> "Turntable Beats - Ayo Feats!"
                        else -> path.substringAfterLast('/') + " - Ayo Feats!"
                    }
                    userMusicTracksList.add(UserSoundItem(
                        filePath = path,
                        displayName = ayoFeatsName,
                        saveToAutoLoad = getHardcodedSelectionState(path, SoundType.MUSIC_TRACK),
                        isHardcoded = true,
                        source = TrackSource.AYO_OFFICIAL
                    ))
                    Log.d("AppViewModel_LoadPage", "Re-added missing hardcoded music track: $ayoFeatsName")
                }
            }
        }
        
        // Sort lists to maintain proper order
        if (type == SoundType.PLATTER_SAMPLE) {
            userPlatterSamplesList.sortWith(compareBy<UserSoundItem> { it.isHardcoded }.thenBy { it.displayName })
        } else {
            userMusicTracksList.sortWith(compareBy<UserSoundItem> { it.isHardcoded }.thenBy { it.displayName })
        }
    }

    fun dismissTrialInfo() {
        showTrialInfoPopup = false
        trackManager.getTrialManager().markTrialInfoShown()
    }
    
    fun upgradeFromTrial() {
        dismissTrialInfo()
        showUpgradePrompt()
    }
    
    fun getTrialManager() = trackManager.getTrialManager()

    fun getSubscriptionStatus() = trackManager.getSubscriptionStatus()

    // === AUDIO SETTINGS PERSISTENCE ===

    private fun loadAudioSettings() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        // Load music master volume (default: 0.9f)
    // Music master volume now fixed at 1.0 (100%). Ignore stored preference.
    musicMasterVolume = 1.0f
    onUpdateMusicMasterVolume(1.0f)

        // Load slipmat damping factor (default: 0.19f - 19%)
    slipmatDampingFactor = prefs.getFloat(KEY_SLIPMAT_DAMPING, 0.32f)
        // SINGLE SOURCE OF TRUTH: Send loaded settings to AudioEngine
        Log.i("AppViewModel_Settings", "STARTUP: Calling onSetSlipmatDamping with: $slipmatDampingFactor")
        onSetSlipmatDamping(slipmatDampingFactor)

        // Load slipmat abruptness (default: 15.0f)
        slipmatAbruptness = prefs.getFloat(KEY_SLIPMAT_ABRUPTNESS, 15.0f)
        // SINGLE SOURCE OF TRUTH: Send loaded settings to AudioEngine
        onSetSlipmatAbruptness(slipmatAbruptness)

        // Load slipmat base friction (default: 15.0f)
        slipmatBaseFriction = prefs.getFloat(KEY_SLIPMAT_BASE_FRICTION, 15.0f)
        // SINGLE SOURCE OF TRUTH: Send loaded settings to AudioEngine
        onSetSlipmatBaseFriction(slipmatBaseFriction)

    // Load touch sensitivity (default: 1.0f - 100%)
    touchSensitivityFactor = prefs.getFloat(KEY_TOUCH_SENSITIVITY, 1.0f).coerceIn(0.75f, 1.25f)
    // Load radius-aware touch (default: false - OFF)
    useRadiusAwareTouch = prefs.getBoolean(KEY_USE_RADIUS_AWARE_TOUCH, false)

    // Load max scratch speed (default 1.5f)
    maxScratchSpeed = prefs.getFloat(KEY_MAX_SCRATCH_SPEED, 1.5f).coerceIn(0.1f, 5.0f)
    applyMaxScratchSpeedToNative?.invoke(maxScratchSpeed)

    Log.d("AppViewModel_Settings", "Loaded audio settings - Volume: $musicMasterVolume, Damping: $slipmatDampingFactor, Abruptness: $slipmatAbruptness, BaseFriction: $slipmatBaseFriction, MaxSpeed: $maxScratchSpeed")
        Log.i("AppViewModel_Settings", "SINGLE SOURCE OF TRUTH: Slipmat settings sent to AudioEngine")
    }

    private fun saveAudioSettings() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit {
            // Persist fixed value for completeness
            putFloat(KEY_MUSIC_MASTER_VOLUME, 1.0f)
            putFloat(KEY_SLIPMAT_DAMPING, slipmatDampingFactor)
            putFloat(KEY_SLIPMAT_ABRUPTNESS, slipmatAbruptness)
            putFloat(KEY_SLIPMAT_BASE_FRICTION, slipmatBaseFriction)
            putFloat(KEY_TOUCH_SENSITIVITY, touchSensitivityFactor)
            putBoolean(KEY_USE_RADIUS_AWARE_TOUCH, useRadiusAwareTouch)
            putFloat(KEY_MAX_SCRATCH_SPEED, maxScratchSpeed)
        }
        Log.d("AppViewModel_Settings", "Saved audio settings - Volume: $musicMasterVolume, Damping: $slipmatDampingFactor, Abruptness: $slipmatAbruptness, BaseFriction: $slipmatBaseFriction, MaxSpeed: $maxScratchSpeed")
    }

    // === PLAYLIST PERSISTENCE AND DUPLICATE DETECTION ===

    /**
     * Check if a track is already in the playlist based on metadata
     * Uses multiple criteria to detect duplicates:
     * 1. Exact file path match
     * 2. Title + Artist match (case-insensitive)
     * 3. Similar title match (for slight variations)
     */
    private fun isDuplicateTrack(newItem: UserSoundItem, playlist: List<UserSoundItem>): Boolean {
        return playlist.any { existingItem ->
            // 1. Exact file path match
            existingItem.filePath == newItem.filePath ||

            // 2. Exact title + artist match (case-insensitive)
            (existingItem.title?.lowercase() == newItem.title?.lowercase() &&
             existingItem.artist?.lowercase() == newItem.artist?.lowercase() &&
             !existingItem.title.isNullOrBlank() && !newItem.title.isNullOrBlank()) ||

            // 3. Similar title match (for slight variations, ignoring common suffixes/prefixes)
            areSimilarTitles(existingItem.title, newItem.title, existingItem.artist, newItem.artist)
        }
    }

    /**
     * Check if two titles are similar enough to be considered duplicates
     * Handles common variations like "Track Name" vs "Track Name (Radio Edit)"
     */
    private fun areSimilarTitles(title1: String?, title2: String?, artist1: String?, artist2: String?): Boolean {
        if (title1.isNullOrBlank() || title2.isNullOrBlank()) return false
        if (artist1?.lowercase() != artist2?.lowercase()) return false

        val cleanTitle1 = cleanTitleForComparison(title1)
        val cleanTitle2 = cleanTitleForComparison(title2)

        // Check if one title contains the other (for cases like "Song" vs "Song (Remix)")
        return cleanTitle1.contains(cleanTitle2, ignoreCase = true) ||
               cleanTitle2.contains(cleanTitle1, ignoreCase = true)
    }

    /**
     * Clean title for comparison by removing common suffixes and normalizing
     */
    private fun cleanTitleForComparison(title: String): String {
        return title.lowercase()
            .replace(Regex("\\s*\\([^)]*\\)\\s*"), "") // Remove parentheses content
            .replace(Regex("\\s*\\[[^]]*\\]\\s*"), "") // Remove brackets content
            .replace(Regex("\\s*-\\s*(remix|edit|version|mix).*$"), "") // Remove remix/edit suffixes
            .trim()
    }

    /**
     * Save current playlist state to persistent storage
     */
    private fun savePlaylistToPersistence() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val playlistJson = JSONArray()

        userMusicTracksList.filter { it.saveToAutoLoad }.forEach { item ->
            val jsonObject = JSONObject().apply {
                put("filePath", item.filePath)
                put("displayName", item.displayName)
                put("title", item.title ?: "")
                put("artist", item.artist ?: "")
                put("durationSeconds", item.durationSeconds)
                put("modificationTimestamp", item.modificationTimestamp)
                put("isHardcoded", item.isHardcoded)
                put("source", item.source.name) // Save source as string
            }
            playlistJson.put(jsonObject)
        }

        prefs.edit { putString(KEY_ACTIVE_PLAYLIST, playlistJson.toString()) }
        Log.d("AppViewModel_Playlist", "Saved playlist with ${playlistJson.length()} tracks")
    }

    /**
     * Load playlist from persistent storage
     */
    private fun loadPlaylistFromPersistence() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val playlistJsonString = prefs.getString(KEY_ACTIVE_PLAYLIST, null)

        if (playlistJsonString != null) {
            try {
                val playlistJson = JSONArray(playlistJsonString)
                val loadedPlaylist = mutableListOf<UserSoundItem>()

                for (i in 0 until playlistJson.length()) {
                    val jsonObject = playlistJson.getJSONObject(i)
                    val filePath = jsonObject.getString("filePath")

                    // PRODUCTION SAFETY: Validate weekly tracks are not unreleased
                    if (filePath.startsWith("weekly/")) {
                        val weeklyTrack = tracks.value.find { track ->
                            "weekly/${track.filename}" == filePath
                        }
                        if (weeklyTrack?.isYetToBeReleased() == true) {
                            Log.w("AppViewModel_Playlist", "Skipping unreleased track from playlist persistence: $filePath")
                            continue // Skip this unreleased track
                        }
                    }

                    val item = UserSoundItem(
                        filePath = filePath,
                        displayName = jsonObject.getString("displayName"),
                        saveToAutoLoad = true, // Playlist items are always auto-loaded
                        isHardcoded = jsonObject.optBoolean("isHardcoded", false),
                        modificationTimestamp = jsonObject.optLong("modificationTimestamp", System.currentTimeMillis()),
                        title = jsonObject.optString("title").takeIf { it.isNotEmpty() },
                        artist = jsonObject.optString("artist").takeIf { it.isNotEmpty() },
                        durationSeconds = jsonObject.optInt("durationSeconds", 0),
                        source = try {
                            TrackSource.valueOf(jsonObject.optString("source", TrackSource.USER_ADDED.name))
                        } catch (e: IllegalArgumentException) {
                            TrackSource.USER_ADDED // Default fallback for invalid values
                        }
                    )

                    // Only add if not already a duplicate
                    if (!isDuplicateTrack(item, loadedPlaylist)) {
                        loadedPlaylist.add(item)
                    }
                }

                // Merge with existing music tracks list, avoiding duplicates
                loadedPlaylist.forEach { playlistItem ->
                    if (!isDuplicateTrack(playlistItem, userMusicTracksList)) {
                        userMusicTracksList.add(playlistItem)
                    }
                }

                Log.d("AppViewModel_Playlist", "Loaded playlist with ${loadedPlaylist.size} tracks")
            } catch (e: Exception) {
                Log.e("AppViewModel_Playlist", "Error loading playlist from persistence", e)
            }
        }
    }

    // === HARDCODED ITEM SELECTION STATE PERSISTENCE ===

    /**
     * Save the selection state of hardcoded items to prevent auto-selection on load screen
     */
    private fun saveHardcodedSelectionStates() {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val selectionsJson = JSONObject()

        // Save platter sample selections
        val platterSelections = JSONObject()
        userPlatterSamplesList.filter { it.isHardcoded }.forEach { item ->
            platterSelections.put(item.filePath, item.saveToAutoLoad)
        }
        selectionsJson.put("platterSamples", platterSelections)

        // Save music track selections
        val musicSelections = JSONObject()
        userMusicTracksList.filter { it.isHardcoded }.forEach { item ->
            musicSelections.put(item.filePath, item.saveToAutoLoad)
        }
        selectionsJson.put("musicTracks", musicSelections)

        prefs.edit { putString(KEY_HARDCODED_SELECTIONS, selectionsJson.toString()) }
        Log.d("AppViewModel_Selections", "Saved hardcoded selection states")
    }

    /**
     * Load the saved selection state for a hardcoded item
     */
    private fun getHardcodedSelectionState(filePath: String, type: SoundType): Boolean {
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val selectionsJsonString = prefs.getString(KEY_HARDCODED_SELECTIONS, null)

        if (selectionsJsonString != null) {
            try {
                val selectionsJson = JSONObject(selectionsJsonString)
                val typeKey = if (type == SoundType.PLATTER_SAMPLE) "platterSamples" else "musicTracks"
                val typeSelections = selectionsJson.optJSONObject(typeKey)

                if (typeSelections != null && typeSelections.has(filePath)) {
                    return typeSelections.getBoolean(filePath)
                }
            } catch (e: Exception) {
                Log.e("AppViewModel_Selections", "Error loading hardcoded selection state", e)
            }
        }

        // Default to true (checked) for hardcoded items to ensure they're available in playlists
        return true
    }
    
    // ================== BILLING INTEGRATION ==================
    
    /**
     * Handle successful upload access purchase (one-time in-app purchase)
     * Grants file upload ability + 1 month of premium access
     */
    fun handleUploadAccessPurchase() {
        val expirationTime = System.currentTimeMillis() + TimeUnit.DAYS.toMillis(30) // 30 days
        
        // Update subscription status to UPLOAD_WITH_MONTH
        trackManager.updateSubscriptionStatus(SubscriptionStatus.UPLOAD_WITH_MONTH)
        
        // Store expiration timestamp for the month access
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putLong("upload_month_expiration", expirationTime)
            .putBoolean("has_permanent_upload_access", true)
            .apply()
            
        Log.i("AppViewModel_Billing", "Upload + Month access granted until ${Date(expirationTime)}")
        
        // Refresh UI and track availability
        updateMusicLibraryWithWeeklyTracks()
    }
    
    /**
     * Handle successful premium subscription purchase
     * Grants full premium access for the subscription duration
     */
    fun handlePremiumSubscriptionPurchase() {
        // Update subscription status to FULL_PREMIUM
        trackManager.updateSubscriptionStatus(SubscriptionStatus.FULL_PREMIUM)
        
        // Clear any upload month expiration since full premium overrides it
        val prefs = getApplication<Application>().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .remove("upload_month_expiration")
            .putBoolean("has_active_subscription", true)
            .apply()
            
        Log.i("AppViewModel_Billing", "Full premium subscription activated")
        
        // Refresh UI and track availability
        updateMusicLibraryWithWeeklyTracks()
    }
    
    /**
     * Launch upload access purchase flow
     */
    fun purchaseUploadAccess(activity: Activity, billingWrapper: BillingClientWrapper) {
        Log.i("AppViewModel_Billing", "Launching upload access purchase")
        billingWrapper.launchUploadAccessPurchase(activity)
    }
    
    /**
     * Launch premium subscription purchase flow
     */
    fun purchasePremiumSubscription(activity: Activity, billingWrapper: BillingClientWrapper) {
        Log.i("AppViewModel_Billing", "Launching premium subscription purchase")
        billingWrapper.launchPremiumSubscription(activity)
    }
}
