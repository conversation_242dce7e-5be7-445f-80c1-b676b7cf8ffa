package com.high.ayodj.monetization

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import android.media.MediaMetadataRetriever
import android.media.MediaScannerConnection
import java.io.IOException
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.util.Calendar
import java.net.URL
import java.net.HttpURLConnection
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.os.Environment
import android.content.ContentValues
import android.provider.MediaStore
import android.os.Build

/**
 * Manages track visibility, subscription logic, and save functionality
 */
class TrackManager(private val context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences("track_manager", Context.MODE_PRIVATE)
    private val trackDatabase = TrackDatabase(context)
    private val metadataExtractor = MP3MetadataExtractor()
    private val batchDownloadManager = BatchDownloadManager(context)
    private val trialManager = TrialManager(context)
    
    private val _visibleTracks = MutableStateFlow<List<Track>>(emptyList())
    val visibleTracks: StateFlow<List<Track>> = _visibleTracks.asStateFlow()
    
    private val _subscriptionStatus = MutableStateFlow(getSubscriptionStatus())
    val subscriptionStatus: StateFlow<SubscriptionStatus> = _subscriptionStatus.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // Download state management
    private val _downloadingTracks = MutableStateFlow<Set<String>>(emptySet())
    val downloadingTracks: StateFlow<Set<String>> = _downloadingTracks.asStateFlow()
    
    private val _downloadErrors = MutableStateFlow<Map<String, String>>(emptyMap())
    val downloadErrors: StateFlow<Map<String, String>> = _downloadErrors.asStateFlow()
    
    companion object {
        private const val TAG = "TrackManager"
        private const val SUBSCRIPTION_KEY = "subscription_status"
        private const val LAST_UPDATE_KEY = "last_monthly_update"
        private const val NOTIFICATIONS_ENABLED_KEY = "notifications_enabled"
        
        // File structure constants
        private const val MONTHLY_PACKS_DIR = "monthly_packs"
        private const val USER_SAVED_DIR = "user_saved"
    }
    
    init {
        Log.d(TAG, "=== TrackManager initializing ===")
        refreshVisibleTracks()
        
        // Auto-create tracks from batch assets if none exist
        val existingTracks = trackDatabase.getAllTracks()
        Log.d(TAG, "Found ${existingTracks.size} existing tracks in database")
        
        if (existingTracks.isEmpty()) {
            Log.d(TAG, "No tracks in database, creating from assets...")
            val initialTracks = createTracksFromAssets()
            addTracks(initialTracks)
            Log.i(TAG, "Auto-created ${initialTracks.size} tracks from batch assets")
        } else {
            Log.d(TAG, "Tracks already exist in database")
            existingTracks.forEach { track ->
                Log.d(TAG, "Existing track: ${track.title} (${track.filename}) - downloadUrl: ${track.downloadUrl}")
            }
        }
        Log.d(TAG, "=== TrackManager initialization complete ===")
    }
    
    /**
     * Get current subscription status - SINGLE SOURCE OF TRUTH
     */
    fun getSubscriptionStatus(): SubscriptionStatus {
        // 1. Dev mode override - always return FULL_PREMIUM
        if (isDevModeEnabled()) {
            Log.d(TAG, "Dev mode enabled - returning FULL_PREMIUM")
            return SubscriptionStatus.FULL_PREMIUM
        }
        
        Log.d(TAG, "Dev mode disabled - checking actual subscription status")
        
        // 2. Check active monthly subscription
        if (hasActiveMonthlySubscription()) {
            return SubscriptionStatus.FULL_PREMIUM
        }
        
        // 3. Check upload purchase with time component
        if (hasUploadPurchase()) {
            val purchaseDate = getUploadPurchaseDate()
            val oneMonthLater = purchaseDate + (30L * 24 * 60 * 60 * 1000) // 30 days in milliseconds
            
            if (System.currentTimeMillis() < oneMonthLater) {
                // Still within the 1-month bonus period
                return SubscriptionStatus.UPLOAD_WITH_MONTH
            } else {
                // Month expired - they keep file imports but lose other premium features
                return SubscriptionStatus.UPLOAD_EXPIRED
            }
        }
        
        // 4. Check trial (initialize if needed)
        if (!trialManager.hasTrialStarted()) {
            trialManager.initializeTrial()
        }
        
        if (trialManager.isTrialActive()) {
            Log.d(TAG, "Trial is active - returning TRIAL")
            return SubscriptionStatus.TRIAL
        }
        
        // 5. Default to free
        Log.d(TAG, "No active subscription - returning FREE")
        return SubscriptionStatus.FREE
    }
    
    /**
     * Check if dev mode is enabled
     */
    private fun isDevModeEnabled(): Boolean {
        val devModePrefs = context.getSharedPreferences("UserSoundPreferences", Context.MODE_PRIVATE)
        return devModePrefs.getBoolean("devMode", false)
    }
    
    /**
     * Check if user has active monthly subscription
     * TODO: Integrate with Google Play Billing
     */
    private fun hasActiveMonthlySubscription(): Boolean {
        val subscriptionPrefs = context.getSharedPreferences("subscription_prefs", Context.MODE_PRIVATE)
        return subscriptionPrefs.getBoolean("has_monthly_subscription", false)
    }
    
    /**
     * Check if user has made upload purchase
     * TODO: Integrate with Google Play Billing
     */
    private fun hasUploadPurchase(): Boolean {
        val subscriptionPrefs = context.getSharedPreferences("subscription_prefs", Context.MODE_PRIVATE)
        return subscriptionPrefs.getBoolean("has_upload_purchase", false)
    }
    
    /**
     * Get upload purchase date for time-based logic
     */
    private fun getUploadPurchaseDate(): Long {
        val subscriptionPrefs = context.getSharedPreferences("subscription_prefs", Context.MODE_PRIVATE)
        return subscriptionPrefs.getLong("upload_purchase_date", 0L)
    }
    
    /**
     * Get trial manager instance
     */
    fun getTrialManager(): TrialManager = trialManager
    
    /**
     * Update subscription status manually (for legacy compatibility)
     */
    fun updateSubscriptionStatus(status: SubscriptionStatus) {
        prefs.edit().putString(SUBSCRIPTION_KEY, status.name).apply()
        _subscriptionStatus.value = status
        refreshVisibleTracks()
        Log.i(TAG, "Subscription status manually updated to: $status")
    }
    
    /**
     * Refresh subscription status from current logic
     */
    fun updateSubscriptionStatus() {
        val newStatus = getSubscriptionStatus()
        _subscriptionStatus.value = newStatus
        refreshVisibleTracks()
        Log.i(TAG, "Subscription status refreshed to: $newStatus")
    }
    
    /**
     * Check if track is visible to current user
     */
    fun checkTrackVisibility(track: Track): Boolean {
        return track.isVisibleTo(getSubscriptionStatus())
    }
    
    /**
     * Unlock a track (make it playable)
     */
    fun unlockTrack(trackId: String) {
        trackDatabase.updateTrackUnlockStatus(trackId, true)
        refreshVisibleTracks()
        Log.i(TAG, "Track unlocked: $trackId")
    }
    
    /**
     * Save track to device with background download (premium only)
     */
    suspend fun saveTrackToDevice(trackId: String): Boolean {
        val subscription = getSubscriptionStatus()
        if (!subscription.canSaveTracks()) {
            Log.w(TAG, "Save attempt denied - requires premium subscription")
            return false
        }
        
        val track = trackDatabase.getTrackById(trackId)
        if (track == null) {
            Log.e(TAG, "Track not found for save: $trackId")
            return false
        }
        
        // Check if already downloading
        if (_downloadingTracks.value.contains(trackId)) {
            Log.w(TAG, "Track $trackId is already being downloaded")
            return false
        }
        
        // Start download
        _downloadingTracks.value = _downloadingTracks.value + trackId
        _downloadErrors.value = _downloadErrors.value - trackId // Clear any previous error
        
        return try {
            // Start background download
            val success = performBackgroundDownload(track)
            if (success) {
                Log.i(TAG, "Background download completed for track: $trackId")
                refreshVisibleTracks()
            } else {
                // Check if this was a missing URL error
                val errorMsg = if (track.downloadUrl == null) {
                    "Download not available for this track"
                } else {
                    "Download failed"
                }
                _downloadErrors.value = _downloadErrors.value + mapOf(trackId to errorMsg)
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Failed to download track: $trackId", e)
            _downloadErrors.value = _downloadErrors.value + mapOf(trackId to (e.message ?: "Unknown error"))
            false
        } finally {
            _downloadingTracks.value = _downloadingTracks.value - trackId
        }
    }
    
    /**
     * Check if track is currently downloading
     */
    fun isTrackDownloading(trackId: String): Boolean {
        return _downloadingTracks.value.contains(trackId)
    }
    
    /**
     * Get download error for track
     */
    fun getDownloadError(trackId: String): String? {
        return _downloadErrors.value[trackId]
    }
    
    /**
     * Clear download error for track
     */
    fun clearDownloadError(trackId: String) {
        _downloadErrors.value = _downloadErrors.value - trackId
    }
    
    /**
     * Download entire batch (premium feature)
     */
    fun downloadBatch(batchNumber: Int) = batchDownloadManager.downloadBatch(batchNumber)
    
    /**
     * Get available batches
     */
    fun getAvailableBatches(): List<Int> = batchDownloadManager.getAvailableBatches()
    
    /**
     * Get tracks for a specific batch
     */
    fun getTracksInBatch(batchNumber: Int): List<Track> {
        return trackDatabase.getAllTracks().filter { it.batch == batchNumber }
            .sortedByDescending { it.getSortingKey() }
    }
    
    /**
     * Initiate background download for a track
     */
    private fun initiateBackgroundDownload(track: Track) {
        // Create download directory structure
        val savedDir = File(context.getExternalFilesDir(null), USER_SAVED_DIR)
        val batchDir = File(savedDir, "batch_${track.batch.toString().padStart(3, '0')}")
        val weekDir = File(batchDir, "week_${track.week}")
        
        if (!weekDir.exists()) {
            weekDir.mkdirs()
        }
        
        val destinationFile = File(weekDir, track.filename)
        
        // Start background download task
        // TODO: Implement actual background download service
        // For now, simulate with immediate file creation
        performSaveToDevice(track, destinationFile)
        
        // Update track with local file path
        trackDatabase.updateTrackLocalPath(track.id, destinationFile.absolutePath)
        refreshVisibleTracks()
    }
    
    /**
     * Perform the actual background download of the MP3 file from the extracted URL
     */
    private suspend fun performBackgroundDownload(track: Track): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.i(TAG, "Starting download for ${track.filename} to public storage")
            
            // Extract download URL from the MP3 metadata if not already available
            val downloadUrl = track.downloadUrl ?: run {
                Log.d(TAG, "Extracting download URL from MP3 metadata for: ${track.filename}")
                try {
                    // Extract from the asset MP3 file
                    val assetManager = context.assets
                    val extractedUrl = metadataExtractor.extractDownloadUrl("weekly/${track.filename}", assetManager)
                    if (extractedUrl != null) {
                        Log.i(TAG, "Extracted download URL for ${track.filename}: $extractedUrl")
                        extractedUrl
                    } else {
                        Log.w(TAG, "No download URL found in metadata for ${track.filename}")
                        // Set a specific error message for missing download URL
                        _downloadErrors.value = _downloadErrors.value + mapOf(track.id to "Download not available for this track")
                        return@withContext false
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to extract download URL for ${track.filename}", e)
                    _downloadErrors.value = _downloadErrors.value + mapOf(track.id to "Unable to access download URL")
                    return@withContext false
                }
            }
            
            Log.i(TAG, "Starting download for ${track.filename} from: $downloadUrl")
            
            // Download the file first to a temporary location
            val tempFile = File.createTempFile("ayodj_download_", ".mp3", context.cacheDir)
            
            try {
                // Perform HTTP download to temp file
                val url = URL(downloadUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.connectTimeout = 30000 // 30 seconds
                connection.readTimeout = 60000 // 60 seconds
                connection.requestMethod = "GET"
                
                val responseCode = connection.responseCode
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    Log.e(TAG, "HTTP error downloading ${track.filename}: $responseCode")
                    return@withContext false
                }
                
                val contentLength = connection.contentLength
                Log.d(TAG, "Download size for ${track.filename}: $contentLength bytes")
                
                // Download the file to temp location
                connection.inputStream.use { input ->
                    tempFile.outputStream().use { output ->
                        val buffer = ByteArray(8192)
                        var bytesRead: Int
                        var totalBytesRead = 0L
                        
                        while (input.read(buffer).also { bytesRead = it } != -1) {
                            output.write(buffer, 0, bytesRead)
                            totalBytesRead += bytesRead
                            
                            // Log progress for large files
                            if (contentLength > 0 && totalBytesRead % (1024 * 1024) == 0L) {
                                val progress = (totalBytesRead * 100 / contentLength)
                                Log.d(TAG, "Download progress for ${track.filename}: $progress%")
                            }
                        }
                        
                        Log.i(TAG, "Download completed for ${track.filename}: $totalBytesRead bytes")
                    }
                }
                
                // Now save the file to public storage using MediaStore
                val publicFilePath = saveToPublicStorage(tempFile, track)
                if (publicFilePath != null) {
                    // Update track with public file path
                    trackDatabase.updateTrackLocalPath(track.id, publicFilePath)
                    Log.i(TAG, "Successfully saved ${track.filename} to public storage: $publicFilePath")
                    return@withContext true
                } else {
                    Log.e(TAG, "Failed to save ${track.filename} to public storage")
                    return@withContext false
                }
                
            } finally {
                // Clean up temp file
                tempFile.delete()
            }
            
        } catch (e: IOException) {
            Log.e(TAG, "Network error downloading ${track.filename}", e)
            return@withContext false
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error downloading ${track.filename}", e)
            return@withContext false
        }
    }
    
    /**
     * Save downloaded file to public storage (Music/AyoDJ folder)
     */
    private fun saveToPublicStorage(tempFile: File, track: Track): String? {
        return try {
            Log.i(TAG, "Determining save method for Android API ${Build.VERSION.SDK_INT}")
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                Log.d(TAG, "Using MediaStore for Android 10+ (API 29+)")
                // Use MediaStore for Android 10+ (API 29+)
                saveToMediaStore(tempFile, track)
            } else {
                Log.d(TAG, "Using legacy storage for Android 9 and below")
                // Use direct file access for older Android versions
                saveToLegacyStorage(tempFile, track)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to public storage", e)
            null
        }
    }
    
    /**
     * Save file using MediaStore (Android 10+)
     */
    private fun saveToMediaStore(tempFile: File, track: Track): String? {
        return try {
            Log.i(TAG, "Saving ${track.filename} to public Music/AyoDJ folder via MediaStore")
            val resolver = context.contentResolver
            val relativePath = "Music/AyoDJ/Batch_${track.batch.toString().padStart(3, '0')}/Week_${track.week}"
            Log.d(TAG, "MediaStore relative path: $relativePath")
            
            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DISPLAY_NAME, track.filename)
                put(MediaStore.MediaColumns.MIME_TYPE, "audio/mpeg")
                put(MediaStore.MediaColumns.RELATIVE_PATH, relativePath)
                put(MediaStore.MediaColumns.IS_PENDING, 1) // Mark as pending during write
            }
            
            val uri = resolver.insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, contentValues)
            if (uri != null) {
                Log.d(TAG, "Created MediaStore entry: $uri")
                resolver.openOutputStream(uri)?.use { outputStream ->
                    tempFile.inputStream().use { inputStream ->
                        val bytesCopied = inputStream.copyTo(outputStream)
                        Log.d(TAG, "Copied $bytesCopied bytes to MediaStore")
                    }
                }
                
                // Mark as not pending
                contentValues.clear()
                contentValues.put(MediaStore.MediaColumns.IS_PENDING, 0)
                resolver.update(uri, contentValues, null, null)
                
                Log.i(TAG, "Successfully saved ${track.filename} to MediaStore: $uri")
                Log.i(TAG, "File should now be accessible in Music/AyoDJ folder by other apps")
                uri.toString() // Return the content URI as the file path
            } else {
                Log.e(TAG, "Failed to create MediaStore entry for ${track.filename}")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error saving ${track.filename} to MediaStore", e)
            null
        }
    }
    
    /**
     * Save file using legacy storage (Android 9 and below)
     */
    private fun saveToLegacyStorage(tempFile: File, track: Track): String? {
        return try {
            Log.i(TAG, "Saving ${track.filename} to public Music/AyoDJ folder via legacy storage")
            val musicDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC)
            val ayodjDir = File(musicDir, "AyoDJ")
            val batchDir = File(ayodjDir, "Batch_${track.batch.toString().padStart(3, '0')}")
            val weekDir = File(batchDir, "Week_${track.week}")
            
            if (!weekDir.exists()) {
                val created = weekDir.mkdirs()
                Log.d(TAG, "Created directory structure: ${weekDir.absolutePath} (success: $created)")
            }
            
            val destinationFile = File(weekDir, track.filename)
            tempFile.copyTo(destinationFile, overwrite = true)
            
            // Trigger media scan so the file shows up in other apps
            MediaScannerConnection.scanFile(
                context,
                arrayOf(destinationFile.absolutePath),
                arrayOf("audio/mpeg")
            ) { path, uri ->
                Log.d(TAG, "Media scan completed for: $path -> $uri")
            }
            
            Log.i(TAG, "Successfully saved ${track.filename} to legacy storage: ${destinationFile.absolutePath}")
            Log.i(TAG, "File should now be accessible in Music/AyoDJ folder by other apps")
            destinationFile.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "Error saving ${track.filename} to legacy storage", e)
            null
        }
    }
    
    /**
     * Check if track is expired (for free users)
     */
    fun isTrackExpired(track: Track): Boolean {
        return track.isExpired() && getSubscriptionStatus() == SubscriptionStatus.FREE
    }
    
    /**
     * Get all visible tracks based on subscription status, sorted with released tracks first
     */
    fun getVisibleTracks(): List<Track> {
        val allTracks = trackDatabase.getAllTracks()
        val subscription = getSubscriptionStatus()
        
        return allTracks.filter { track ->
            track.isVisibleTo(subscription)
        }.sortedWith(compareBy<Track> { track ->
            // Primary sort: Released tracks first (0), unreleased tracks last (1)
            if (track.isYetToBeReleased()) 1 else 0
        }.thenByDescending { track ->
            // Secondary sort: Within each group, newest first
            track.getSortingKey()
        })
    }
    
    /**
     * Get tracks that are expired but could be accessed with premium
     */
    fun getExpiredTracks(): List<Track> {
        val currentStatus = getSubscriptionStatus()
        if (currentStatus.canAccessBacklog()) {
            return emptyList() // Users with backlog access don't have expired tracks
        }
        
        val allTracks = trackDatabase.getAllTracks()
        return allTracks.filter { it.isExpired() }
            .sortedByDescending { it.getSortingKey() }
    }
    
    /**
     * Check if user can access backlog
     */
    fun canAccessBacklog(): Boolean {
        return getSubscriptionStatus().canAccessBacklog()
    }
    
    /**
     * Add new tracks to the database
     */
    fun addTracks(tracks: List<Track>) {
        trackDatabase.insertTracks(tracks)
        refreshVisibleTracks()
        Log.i(TAG, "Added ${tracks.size} tracks to database")
    }
    
    /**
     * Helper to extract MP3 metadata from assets/weekly
     * Copies the asset to a cache file and uses its path for MediaMetadataRetriever
     */
    private fun getMp3MetadataFromAssets(filename: String): Triple<String, String, Int> {
        val assetManager = context.assets
        var title = filename
        var artist = "Unknown Artist"
        var duration = 0
        val cacheFile = File.createTempFile("meta_temp_", ".mp3", context.cacheDir)
        try {
            assetManager.open("weekly/$filename").use { inputStream ->
                cacheFile.outputStream().use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }
            val mmr = MediaMetadataRetriever()
            mmr.setDataSource(cacheFile.absolutePath)
            title = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_TITLE) ?: filename
            artist = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST) ?: "Unknown Artist"
            duration = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toIntOrNull() ?: 0
            mmr.release()
        } catch (e: Exception) {
            Log.w(TAG, "Could not read metadata for $filename: ${e.message}")
        } finally {
            cacheFile.delete()
        }
        return Triple(title, artist, duration / 1000) // duration in seconds
    }

    /**
     * Create tracks from batch assets in the new format
     */
    fun createTracksFromAssets(): List<Track> {
        val tracks = mutableListOf<Track>()
        val assetManager = context.assets
        
        try {
            val assetFiles = assetManager.list("weekly")?.filter { it.endsWith(".mp3") } ?: emptyList()
            Log.d(TAG, "Found ${assetFiles.size} MP3 files in assets")
            
            assetFiles.forEach { filename ->
                val metadata = metadataExtractor.extractTrackData(filename, assetManager)
                if (metadata != null) {
                    // Use metadata from file if available, fallback to CSV
                    val downloadUrl = metadata.downloadUrl 
                        ?: batchDownloadManager.getFallbackUrl(filename)
                    
                    val releaseTimestamp = metadataExtractor.calculateReleaseTimestamp(metadata.week, metadata.year)
                    
                    val track = Track(
                        id = "track_${metadata.batch}_${metadata.week}_${metadata.year}_${metadata.variant}",
                        filename = filename,
                        title = metadata.title,
                        artist = metadata.artist,
                        batch = metadata.batch,
                        week = metadata.week,
                        year = metadata.year,
                        variant = metadata.variant,
                        releaseTimestamp = releaseTimestamp,
                        // CRITICAL: Only unlock tracks that have actually been released (not future tracks)
                        isUnlocked = releaseTimestamp <= System.currentTimeMillis(), 
                        isPremiumOnly = false,
                        notificationSent = releaseTimestamp <= System.currentTimeMillis(),
                        downloadUrl = downloadUrl,
                        fileSizeBytes = 8_000_000L, // Estimate
                        durationSeconds = metadata.durationSeconds
                    )
                    tracks.add(track)
                    Log.d(TAG, "Created track: ${track.getDisplayName()} - ${track.title}")
                } else {
                    Log.w(TAG, "Could not extract metadata from $filename")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error reading batch assets: ${e.message}")
        }
        
        Log.i(TAG, "Created ${tracks.size} tracks from assets")
        return tracks.sortedByDescending { it.getSortingKey() }
    }
    
    /**
     * Get current week of year for auto-unlock logic
     */
    private fun getCurrentWeekOfYear(): Int {
        return Calendar.getInstance().get(Calendar.WEEK_OF_YEAR)
    }
    
    /**
     * Refresh visible tracks list
     */
    private fun refreshVisibleTracks() {
        _visibleTracks.value = getVisibleTracks()
    }
    
    /**
     * Perform actual save to device operation
     */
    private fun performSaveToDevice(track: Track, destinationFile: File) {
        try {
            // TODO: Implement actual file download from track.downloadUrl
            // For now, create a placeholder file with track metadata
            val trackInfo = """
                Track: ${track.title}
                Artist: ${track.artist}
                Release: Batch ${track.batch}, Week ${track.week}, ${track.year}
                Variant: ${track.variant.uppercase()}
                Duration: ${track.durationSeconds}s
                File Size: ${track.fileSizeBytes} bytes
                Download URL: ${track.downloadUrl ?: "N/A"}
            """.trimIndent()
            
            destinationFile.writeText(trackInfo)
            Log.i(TAG, "Track file created at: ${destinationFile.absolutePath}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create track file", e)
            throw e
        }
    }
    
    /**
     * Check if notifications are enabled
     */
    fun areNotificationsEnabled(): Boolean {
        return prefs.getBoolean(NOTIFICATIONS_ENABLED_KEY, true)
    }
    
    /**
     * Set notification preference
     */
    fun setNotificationsEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(NOTIFICATIONS_ENABLED_KEY, enabled).apply()
    }
    
    /**
     * Get track by ID - public access method
     */
    fun getTrackById(trackId: String): Track? {
        return trackDatabase.getTrackById(trackId)
    }
}

/**
 * Simple database wrapper for tracks
 * In production, this would use Room or SQLite
 */
private class TrackDatabase(private val context: Context) {
    private val prefs = context.getSharedPreferences("track_database", Context.MODE_PRIVATE)
    
    fun getAllTracks(): List<Track> {
        val tracksJson = prefs.getString("tracks", "[]")
        return parseTracksFromJson(tracksJson ?: "[]")
    }
    
    fun getTrackById(trackId: String): Track? {
        return getAllTracks().find { it.id == trackId }
    }
    
    fun insertTracks(tracks: List<Track>) {
        val existingTracks = getAllTracks().toMutableList()
        tracks.forEach { newTrack ->
            val existingIndex = existingTracks.indexOfFirst { it.id == newTrack.id }
            if (existingIndex >= 0) {
                existingTracks[existingIndex] = newTrack
            } else {
                existingTracks.add(newTrack)
            }
        }
        saveTracksToJson(existingTracks)
    }
    
    fun updateTrackUnlockStatus(trackId: String, isUnlocked: Boolean) {
        val tracks = getAllTracks().toMutableList()
        val trackIndex = tracks.indexOfFirst { it.id == trackId }
        if (trackIndex >= 0) {
            tracks[trackIndex] = tracks[trackIndex].copy(isUnlocked = isUnlocked)
            saveTracksToJson(tracks)
        }
    }
    
    fun updateTrackLocalPath(trackId: String, localPath: String) {
        val tracks = getAllTracks().toMutableList()
        val trackIndex = tracks.indexOfFirst { it.id == trackId }
        if (trackIndex >= 0) {
            tracks[trackIndex] = tracks[trackIndex].copy(localFilePath = localPath)
            saveTracksToJson(tracks)
        }
    }
    
    private fun parseTracksFromJson(json: String): List<Track> {
        return try {
            val jsonArray = JSONArray(json)
            val tracks = mutableListOf<Track>()
            
            for (i in 0 until jsonArray.length()) {
                val jsonObject = jsonArray.getJSONObject(i)
                val track = Track(
                    id = jsonObject.getString("id"),
                    filename = jsonObject.getString("filename"),
                    title = jsonObject.getString("title"),
                    artist = jsonObject.optString("artist", "AyoDJ Collective"),
                    batch = jsonObject.getInt("batch"),
                    week = jsonObject.getInt("week"),
                    year = jsonObject.getInt("year"),
                    variant = jsonObject.getString("variant"),
                    releaseTimestamp = jsonObject.getLong("releaseTimestamp"),
                    isUnlocked = jsonObject.getBoolean("isUnlocked"),
                    isPremiumOnly = jsonObject.getBoolean("isPremiumOnly"),
                    notificationSent = jsonObject.getBoolean("notificationSent"),
                    localFilePath = jsonObject.optString("localFilePath").takeIf { it.isNotEmpty() },
                    downloadUrl = jsonObject.optString("downloadUrl").takeIf { it.isNotEmpty() },
                    fileSizeBytes = jsonObject.optLong("fileSizeBytes", 0L),
                    durationSeconds = jsonObject.optInt("durationSeconds", 0)
                )
                tracks.add(track)
            }
            
            tracks
        } catch (e: Exception) {
            Log.e("TrackDatabase", "Error parsing tracks JSON", e)
            emptyList()
        }
    }
    
    private fun saveTracksToJson(tracks: List<Track>) {
        try {
            val jsonArray = JSONArray()
            tracks.forEach { track ->
                val jsonObject = JSONObject().apply {
                    put("id", track.id)
                    put("filename", track.filename)
                    put("title", track.title)
                    put("artist", track.artist)
                    put("batch", track.batch)
                    put("week", track.week)
                    put("year", track.year)
                    put("variant", track.variant)
                    put("releaseTimestamp", track.releaseTimestamp)
                    put("isUnlocked", track.isUnlocked)
                    put("isPremiumOnly", track.isPremiumOnly)
                    put("notificationSent", track.notificationSent)
                    put("localFilePath", track.localFilePath ?: "")
                    put("downloadUrl", track.downloadUrl ?: "")
                    put("fileSizeBytes", track.fileSizeBytes)
                    put("durationSeconds", track.durationSeconds)
                }
                jsonArray.put(jsonObject)
            }
            
            prefs.edit().putString("tracks", jsonArray.toString()).apply()
        } catch (e: Exception) {
            Log.e("TrackDatabase", "Error saving tracks JSON", e)
        }
    }
}
